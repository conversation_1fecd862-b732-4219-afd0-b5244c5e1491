const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const { upload, uploadToCloudinary } = require('../middleware/upload');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar todas as transações do usuário
router.get('/', async (req, res) => {
  try {
    const transactions = await prisma.transaction.findMany({
      where: {
        userId: req.user.id
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true,
        tags: true
      },
      orderBy: {
        date: 'desc'
      }
    })

    res.json(transactions)
  } catch (error) {
    console.error('Erro ao buscar transações:', error)
    res.status(500).json({ error: 'Erro ao buscar transações' })
  }
})

// Criar uma nova transação
router.post('/', upload.single('receipt'), async (req, res) => {
  try {
    const { description, amount, type, date, categoryId, bankId, paymentMethodId, transactionContactId, installments, tagIds } = req.body

    // Validar dados obrigatórios
    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Dados obrigatórios não fornecidos' })
    }

    // Processar tags
    let parsedTagIds = []
    if (tagIds) {
      try {
        parsedTagIds = JSON.parse(tagIds)
        // Verificar se todas as tags pertencem ao usuário
        const tags = await prisma.tag.findMany({
          where: {
            id: { in: parsedTagIds },
            userId: req.user.id
          }
        })
        if (tags.length !== parsedTagIds.length) {
          return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
        }
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' })
      }
    }

    // Upload do comprovante se fornecido
    let receiptUrl = null;
    if (req.file) {
      try {
        const uploadResult = await uploadToCloudinary(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );
        receiptUrl = uploadResult.secure_url;
      } catch (uploadError) {
        console.error('Erro ao fazer upload do comprovante:', uploadError);
        // Continuar sem o comprovante se o upload falhar
      }
    }

    // Atualizar saldo do banco se especificado
    if (bankId) {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId: req.user.id }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      // Atualizar saldo do banco
      const balanceChange = type === 'INCOME' ? parseFloat(amount) : -parseFloat(amount);
      await prisma.bank.update({
        where: { id: bankId },
        data: { currentBalance: bank.currentBalance + balanceChange }
      });
    }

    // Atualizar fatura do cartão de crédito se especificado (apenas para transações não parceladas)
    const numInstallments = parseInt(installments) || 1;
    const isInstallment = numInstallments > 1 && paymentMethodId;

    if (paymentMethodId && type === 'EXPENSE' && !isInstallment) {
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId: req.user.id, type: 'CREDIT' }
      });

      if (paymentMethod) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: paymentMethod.currentBill + parseFloat(amount),
            isBillPaid: false
          }
        });
      }
    }

    if (isInstallment) {
      // Verificar se é cartão de crédito
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId: req.user.id, type: 'CREDIT' }
      });

      if (!paymentMethod) {
        return res.status(400).json({ error: 'Parcelamento só é permitido para cartões de crédito' });
      }

      // Criar transação pai (registro de controle - não aparece na lista principal)
      const parentTransaction = await prisma.transaction.create({
        data: {
          description: `[CONTROLE] ${description} (${numInstallments}x - Total: R$ ${parseFloat(amount).toFixed(2)})`,
          amount: parseFloat(amount), // Valor total para controle
          type,
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          transactionContactId: transactionContactId || null,
          receiptUrl: receiptUrl,
          installments: numInstallments,
          currentInstallment: 0, // 0 indica que é transação pai/controle
          date: date ? new Date(date) : new Date(),
          userId: req.user.id,
          tags: {
            connect: parsedTagIds.map(id => ({ id }))
          }
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true,
          transactionContact: true,
          tags: true
        }
      });

      // Criar parcelas
      const installmentAmount = parseFloat(amount) / numInstallments;
      const installmentTransactions = [];

      for (let i = 1; i <= numInstallments; i++) {
        const installmentDate = new Date(date ? new Date(date) : new Date());
        installmentDate.setMonth(installmentDate.getMonth() + (i - 1));

        const installmentTransaction = await prisma.transaction.create({
          data: {
            description: `${description} (${i}/${numInstallments})`,
            amount: installmentAmount,
            type,
            categoryId: categoryId || null,
            bankId: bankId || null,
            paymentMethodId: paymentMethodId || null,
            transactionContactId: transactionContactId || null,
            installments: numInstallments,
            currentInstallment: i,
            parentTransactionId: parentTransaction.id,
            date: installmentDate,
            isPaid: paymentMethod.type === 'CREDIT' ? false : true, // Cartão de crédito não é pago imediatamente
            userId: req.user.id,
            tags: {
              connect: parsedTagIds.map(id => ({ id }))
            }
          },
          include: {
            category: true,
            bank: true,
            paymentMethod: true,
            transactionContact: true,
            tags: true
          }
        });

        installmentTransactions.push(installmentTransaction);

        // Adicionar à fatura do cartão apenas a primeira parcela
        if (i === 1 && type === 'EXPENSE' && paymentMethod.type === 'CREDIT') {
          await prisma.paymentMethod.update({
            where: { id: paymentMethodId },
            data: {
              currentBill: paymentMethod.currentBill + installmentAmount,
              isBillPaid: false
            }
          });
        }
      }

      res.status(201).json({
        ...parentTransaction,
        installmentTransactions
      });
    } else {
      // Transação normal (sem parcelamento)
      // Verificar se é cartão de crédito para definir isPaid
      let isPaidValue = true; // Por padrão, transações são pagas imediatamente
      if (paymentMethodId) {
        const paymentMethod = await prisma.paymentMethod.findFirst({
          where: { id: paymentMethodId, userId: req.user.id }
        });
        if (paymentMethod && paymentMethod.type === 'CREDIT') {
          isPaidValue = false; // Cartão de crédito não é pago imediatamente
        }
      }

      const transaction = await prisma.transaction.create({
        data: {
          description,
          amount: parseFloat(amount),
          type,
          date: date ? new Date(date) : new Date(),
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          transactionContactId: transactionContactId || null,
          isPaid: isPaidValue,
          userId: req.user.id,
          receiptUrl: receiptUrl,
          installments: 1,
          currentInstallment: 1,
          tags: {
            connect: parsedTagIds.map(id => ({ id }))
          }
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true,
          transactionContact: true,
          tags: true
        }
      });

      res.status(201).json(transaction);
    }
  } catch (error) {
    console.error('Erro ao criar transação:', error);
    res.status(500).json({ error: 'Erro ao criar transação' });
  }
});

// Atualizar uma transação
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const { description, amount, type, date, categoryId, bankId, paymentMethodId, transactionContactId, tagIds } = req.body

    // Validar dados obrigatórios
    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Dados obrigatórios não fornecidos' })
    }

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    })

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' })
    }

    // Processar tags
    let parsedTagIds = []
    if (tagIds) {
      try {
        parsedTagIds = Array.isArray(tagIds) ? tagIds : JSON.parse(tagIds)
        // Verificar se todas as tags pertencem ao usuário
        const tags = await prisma.tag.findMany({
          where: {
            id: { in: parsedTagIds },
            userId: req.user.id
          }
        })
        if (tags.length !== parsedTagIds.length) {
          return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
        }
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' })
      }
    }

    // Atualizar transação
    const transaction = await prisma.transaction.update({
      where: { id },
      data: {
        description,
        amount: parseFloat(amount),
        type,
        date: date ? new Date(date) : existingTransaction.date,
        categoryId: categoryId || null,
        bankId: bankId || null,
        paymentMethodId: paymentMethodId || null,
        transactionContactId: transactionContactId || null,
        tags: {
          set: parsedTagIds.map(id => ({ id }))
        }
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true,
        tags: true
      }
    })

    res.json(transaction)
  } catch (error) {
    console.error('Erro ao atualizar transação:', error)
    res.status(500).json({ error: 'Erro ao atualizar transação' })
  }
})

// Buscar detalhes das parcelas de uma transação
router.get('/:id/installments', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const transaction = await prisma.transaction.findFirst({
      where: { id, userId },
      include: {
        category: true,
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!transaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    let installments = [];

    // Se a transação tem parcelas (installments > 1)
    if (transaction.installments > 1) {
      // Se é uma transação pai (parentTransactionId é null), buscar todas as parcelas filhas
      if (!transaction.parentTransactionId) {
        installments = await prisma.transaction.findMany({
          where: {
            parentTransactionId: id,
            userId
          },
          orderBy: { currentInstallment: 'asc' },
          include: {
            category: true,
            paymentMethod: {
              include: {
                bank: true
              }
            }
          }
        });
      } else {
        // Se é uma parcela filha, buscar todas as parcelas irmãs
        installments = await prisma.transaction.findMany({
          where: {
            parentTransactionId: transaction.parentTransactionId,
            userId
          },
          orderBy: { currentInstallment: 'asc' },
          include: {
            category: true,
            paymentMethod: {
              include: {
                bank: true
              }
            }
          }
        });
      }
    } else {
      // Se não tem parcelas, retornar apenas a transação atual
      installments = [transaction];
    }

    res.json(installments);
  } catch (error) {
    console.error('Erro ao buscar parcelas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar transação
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    await prisma.transaction.delete({
      where: { id }
    });

    res.json({ message: 'Transação deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
