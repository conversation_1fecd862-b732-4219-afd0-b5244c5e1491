/**
 * @swagger
 * components:
 *   schemas:
 *     Transaction:
 *       type: object
 *       required:
 *         - description
 *         - amount
 *         - type
 *       properties:
 *         id:
 *           type: string
 *           description: ID único da transação
 *         description:
 *           type: string
 *           description: Descrição da transação
 *         amount:
 *           type: number
 *           description: Valor da transação
 *         type:
 *           type: string
 *           enum: [INCOME, EXPENSE, INVESTMENT, LOAN]
 *           description: Tipo da transação
 *         date:
 *           type: string
 *           format: date-time
 *           description: Data da transação
 */

const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const dateService = require('../services/dateService');
const { INSTALLMENT_STATUS } = require('../constants/status');
const billService = require('../services/billService');
const { upload, uploadToCloudinary } = require('../middleware/upload');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

/**
 * @swagger
 * /transactions:
 *   get:
 *     summary: Lista todas as transações do usuário
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de transações retornada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Transaction'
 *       401:
 *         description: Token de autenticação inválido
 *       500:
 *         description: Erro interno do servidor
 */
// Listar todas as transações do usuário
router.get('/', async (req, res) => {
  try {
    const transactions = await prisma.transaction.findMany({
      where: {
        userId: req.user.id
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true,
        tags: true,
        loan: {
          include: {
            contact: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    })

    res.json(transactions)
  } catch (error) {
    console.error('Erro ao buscar transações:', error)
    res.status(500).json({ error: 'Erro ao buscar transações' })
  }
})

// Criar uma nova transação
router.post('/', upload.single('receipt'), async (req, res) => {
  try {
    const { description, amount, type, date, categoryId, bankId, paymentMethodId, transactionContactId, installments, tagIds } = req.body

    // Validar dados obrigatórios
    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Dados obrigatórios não fornecidos' })
    }

    // Processar tags
    let parsedTagIds = []
    if (tagIds) {
      try {
        parsedTagIds = JSON.parse(tagIds)
        // Verificar se todas as tags pertencem ao usuário
        const tags = await prisma.tag.findMany({
          where: {
            id: { in: parsedTagIds },
            userId: req.user.id
          }
        })
        if (tags.length !== parsedTagIds.length) {
          return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
        }
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' })
      }
    }

    // Upload do comprovante se fornecido
    let receiptUrl = null;
    if (req.file) {
      try {
        const uploadResult = await uploadToCloudinary(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );
        receiptUrl = uploadResult.secure_url;
      } catch (uploadError) {
        console.error('Erro ao fazer upload do comprovante:', uploadError);
        // Continuar sem o comprovante se o upload falhar
      }
    }

    // ✅ ATUALIZAR SALDO DO BANCO APENAS PARA TRANSAÇÕES NÃO PARCELADAS
    const numInstallments = parseInt(installments) || 1;
    const isInstallment = numInstallments > 1 && paymentMethodId;

    if (bankId && !isInstallment) {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId: req.user.id }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      // Atualizar saldo do banco apenas para transações normais
      const balanceChange = type === 'INCOME' ? parseFloat(amount) : -parseFloat(amount);
      await prisma.bank.update({
        where: { id: bankId },
        data: { currentBalance: bank.currentBalance + balanceChange }
      });
    }

    // ✅ VERIFICAR FATURA EM ATRASO PARA TRANSAÇÕES NORMAIS DE CARTÃO
    if (paymentMethodId && type === 'EXPENSE' && !isInstallment) {
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId: req.user.id, type: 'CREDIT' },
        include: { bank: true }
      });

      if (paymentMethod) {
        // ✅ REGRA 4: Verificar se pode criar transação (fatura não está em atraso)
        if (paymentMethod.bank) {
          const canCreate = await billService.canCreateTransaction(paymentMethod.bank.id, req.user.id);
          if (!canCreate.canCreate) {
            return res.status(400).json({
              error: canCreate.reason,
              billAmount: canCreate.billAmount
            });
          }
        }

        // Atualizar fatura do cartão de crédito
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: paymentMethod.currentBill + parseFloat(amount),
            isBillPaid: false
          }
        });
      }
    }

    if (isInstallment) {
      // Verificar se é cartão de crédito
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId: req.user.id, type: 'CREDIT' },
        include: { bank: true }
      });

      if (!paymentMethod) {
        return res.status(400).json({ error: 'Parcelamento só é permitido para cartões de crédito' });
      }

      // ✅ REGRA 4: Verificar se pode criar transação (fatura não está em atraso)
      if (paymentMethod.bank) {
        const canCreate = await billService.canCreateTransaction(paymentMethod.bank.id, req.user.id);
        if (!canCreate.canCreate) {
          return res.status(400).json({
            error: canCreate.reason,
            billAmount: canCreate.billAmount
          });
        }
      }

      // Criar transação pai (registro de controle - NÃO SOMA NA FATURA)
      const parentTransaction = await prisma.transaction.create({
        data: {
          description: `[CONTROLE] ${description} (${numInstallments}x - Total: R$ ${parseFloat(amount).toFixed(2)})`,
          amount: parseFloat(amount), // Valor total para controle
          type,
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          transactionContactId: transactionContactId || null,
          receiptUrl: receiptUrl,
          installments: numInstallments,
          currentInstallment: 0, // 0 indica que é transação pai/controle
          date: date ? new Date(date) : new Date(),
          isPaid: true, // ✅ MARCADA COMO PAGA PARA NÃO SOMAR NA FATURA
          userId: req.user.id,
          tags: {
            connect: parsedTagIds.map(id => ({ id }))
          }
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true,
          transactionContact: true,
          tags: true
        }
      });

      // ✅ CRIAR CONTROLE DE PARCELAS
      const installmentControl = await prisma.installmentControl.create({
        data: {
          parentTransactionId: parentTransaction.id,
          paymentMethodId,
          totalAmount: parseFloat(amount),
          installmentAmount: parseFloat(amount) / numInstallments,
          totalInstallments: numInstallments,
          currentInstallment: 1, // Próxima parcela a ser processada
          description,
          startDate: date ? new Date(date) : new Date(),
          userId: req.user.id
        }
      });

      // ✅ CRIAR TODAS AS PARCELAS COM LÓGICA DE FECHAMENTO DE FATURA
      const installmentAmount = parseFloat(amount) / numInstallments;
      const installmentTransactions = [];
      const transactionDate = new Date(date ? new Date(date) : await dateService.getCurrentDate());

      // Buscar dados do banco para verificar dia de fechamento da fatura
      const bank = await prisma.bank.findFirst({
        where: { id: paymentMethod.bankId, userId: req.user.id }
      });

      // ✅ CALCULAR DISTRIBUIÇÃO CORRETA: FATURA ATUAL vs SALDO vs RESERVADO
      let totalForBill = 0;
      let totalForBankBalance = 0;
      let totalReserved = 0;

      for (let i = 1; i <= numInstallments; i++) {
        const installmentDate = new Date(transactionDate);
        installmentDate.setMonth(installmentDate.getMonth() + (i - 1));

        // ✅ DETERMINAR DESTINO DA PARCELA (3 POSSIBILIDADES)
        let shouldGoToBill = false;
        let shouldReduceBankBalance = false;
        let shouldStayReserved = false;

        if (bank && bank.billDueDay && type === 'EXPENSE') {
          // ✅ USAR SERVIÇO DE CICLO DE FATURAMENTO PARA LÓGICA COMPLETA
          const billingCycleService = require('../services/billingCycleService');
          const billingInfo = await billingCycleService.shouldGoToBill(installmentDate, bank.id, req.user.id);

          shouldGoToBill = billingInfo.shouldGoToBill;
          shouldReduceBankBalance = billingInfo.shouldReduceBankBalance;
          shouldStayReserved = billingInfo.shouldStayReserved;

          let status = shouldGoToBill ? 'FATURA ATUAL' :
                      shouldReduceBankBalance ? 'REDUZ SALDO' :
                      'FICA RESERVADA';
          console.log(`📅 Parcela ${i} (${installmentDate.toLocaleDateString()}) - ${status}`);
        }

        // ✅ CONTABILIZAR VALORES POR DESTINO
        if (shouldGoToBill) {
          totalForBill += installmentAmount;
        } else if (shouldReduceBankBalance) {
          totalForBankBalance += installmentAmount;
        } else if (shouldStayReserved) {
          totalReserved += installmentAmount;
        }

        // ✅ RECALCULAR LÓGICA PARA ESTA PARCELA ESPECÍFICA
        const billingCycleService = require('../services/billingCycleService');
        const billingInfo = await billingCycleService.shouldGoToBill(installmentDate, bank.id, req.user.id);
        shouldGoToBill = billingInfo.shouldGoToBill;
        shouldReduceBankBalance = billingInfo.shouldReduceBankBalance;
        shouldStayReserved = billingInfo.shouldStayReserved;

        // ✅ DETERMINAR STATUS DA PARCELA:
        let isPaidStatus = false;
        let installmentStatusValue = INSTALLMENT_STATUS.PENDING;

        if (shouldReduceBankBalance) {
          isPaidStatus = true;
          installmentStatusValue = INSTALLMENT_STATUS.BANK_BALANCE;
        } else if (shouldGoToBill) {
          isPaidStatus = false;
          installmentStatusValue = INSTALLMENT_STATUS.CURRENT_BILL;
        } else if (shouldStayReserved) {
          isPaidStatus = true;
          installmentStatusValue = INSTALLMENT_STATUS.RESERVED;
        }

        const installmentTransaction = await prisma.transaction.create({
          data: {
            description: description, // ✅ SEM NUMERAÇÃO - apenas o nome original
            amount: installmentAmount,
            type,
            categoryId: categoryId || null,
            bankId: shouldReduceBankBalance ? paymentMethod.bankId : (bankId || null),
            paymentMethodId: paymentMethodId || null,
            transactionContactId: transactionContactId || null,
            installments: numInstallments,
            currentInstallment: i,
            parentTransactionId: parentTransaction.id,
            date: installmentDate,
            isPaid: isPaidStatus,
            installmentStatus: installmentStatusValue,
            userId: req.user.id,
            tags: {
              connect: parsedTagIds.map(id => ({ id }))
            }
          },
          include: {
            category: true,
            bank: true,
            paymentMethod: true,
            transactionContact: true,
            tags: true
          }
        });

        installmentTransactions.push(installmentTransaction);

        // ✅ PROCESSAR CONFORME A LÓGICA COMPLETA DE FECHAMENTO
        if (shouldReduceBankBalance && bank) {
          // Reduzir saldo do banco imediatamente (fatura já foi paga)
          await prisma.bank.update({
            where: { id: bank.id },
            data: {
              currentBalance: {
                decrement: installmentAmount
              }
            }
          });
          console.log(`💰 Saldo do banco reduzido em R$ ${installmentAmount} (parcela ${i} - fatura já paga)`);
        } else if (shouldGoToBill && type === 'EXPENSE' && paymentMethod.type === 'CREDIT') {
          // Adicionar à fatura atual do cartão
          await prisma.paymentMethod.update({
            where: { id: paymentMethodId },
            data: {
              currentBill: paymentMethod.currentBill + installmentAmount,
              isBillPaid: false
            }
          });
          console.log(`💳 Parcela ${i} adicionada à fatura atual: R$ ${installmentAmount}`);
        } else if (shouldStayReserved) {
          // Não faz nada agora - fica reservada para próximo ciclo
          console.log(`⏳ Parcela ${i} fica reservada para próxima fatura: R$ ${installmentAmount}`);
        }
      }

      // ✅ REDUZIR LIMITE DISPONÍVEL PELO VALOR QUE AFETA O CRÉDITO (FATURA + RESERVADO)
      const totalThatAffectsCredit = totalForBill + totalReserved;

      if (paymentMethod.bankId && type === 'EXPENSE' && totalThatAffectsCredit > 0) {
        const bankForLimit = await prisma.bank.findFirst({
          where: { id: paymentMethod.bankId, userId: req.user.id }
        });

        if (bankForLimit) {
          // Verificar se há limite disponível suficiente
          if (bankForLimit.availableLimit < totalThatAffectsCredit) {
            return res.status(400).json({
              error: 'Limite de crédito insuficiente',
              available: bankForLimit.availableLimit,
              required: totalThatAffectsCredit,
              totalAmount: parseFloat(amount),
              billAmount: totalForBill,
              bankAmount: totalForBankBalance,
              reservedAmount: totalReserved
            });
          }

          // Reduzir limite disponível pelo valor que afeta o crédito
          await prisma.bank.update({
            where: { id: paymentMethod.bankId },
            data: {
              availableLimit: {
                decrement: totalThatAffectsCredit
              }
            }
          });

          console.log(`💳 LIMITE REDUZIDO CORRETAMENTE (LÓGICA REAL):`);
          console.log(`   Valor total da compra: R$ ${parseFloat(amount)}`);
          console.log(`   Valor que vai para fatura atual: R$ ${totalForBill}`);
          console.log(`   Valor que reduz saldo (já pago): R$ ${totalForBankBalance}`);
          console.log(`   Valor reservado (próxima fatura): R$ ${totalReserved}`);
          console.log(`   Total que afeta crédito: R$ ${totalThatAffectsCredit}`);
          console.log(`   Limite reduzido em: R$ ${totalThatAffectsCredit}`);
          console.log(`   Limite anterior: R$ ${bankForLimit.availableLimit}`);
          console.log(`   Limite após redução: R$ ${bankForLimit.availableLimit - totalThatAffectsCredit}`);
        }
      }

      res.status(201).json({
        message: 'Transação parcelada criada com sucesso',
        controlTransaction: parentTransaction,
        installmentControl,
        installments: installmentTransactions
      });
    } else {
      // Transação normal (sem parcelamento)
      // Verificar se é cartão de crédito para definir isPaid
      let isPaidValue = true; // Por padrão, transações são pagas imediatamente
      if (paymentMethodId) {
        const paymentMethod = await prisma.paymentMethod.findFirst({
          where: { id: paymentMethodId, userId: req.user.id }
        });
        if (paymentMethod && paymentMethod.type === 'CREDIT') {
          isPaidValue = false; // Cartão de crédito não é pago imediatamente
        }
      }

      const transaction = await prisma.transaction.create({
        data: {
          description,
          amount: parseFloat(amount),
          type,
          date: date ? new Date(date) : new Date(),
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          transactionContactId: transactionContactId || null,
          isPaid: isPaidValue,
          userId: req.user.id,
          receiptUrl: receiptUrl,
          installments: 1,
          currentInstallment: 1,
          tags: {
            connect: parsedTagIds.map(id => ({ id }))
          }
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true,
          transactionContact: true,
          tags: true
        }
      });

      res.status(201).json(transaction);
    }
  } catch (error) {
    console.error('Erro ao criar transação:', error);
    res.status(500).json({ error: 'Erro ao criar transação' });
  }
});

// Atualizar uma transação
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const { description, amount, type, date, categoryId, bankId, paymentMethodId, transactionContactId, tagIds } = req.body

    // Validar dados obrigatórios
    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Dados obrigatórios não fornecidos' })
    }

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    })

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' })
    }

    // Processar tags
    let parsedTagIds = []
    if (tagIds) {
      try {
        parsedTagIds = Array.isArray(tagIds) ? tagIds : JSON.parse(tagIds)
        // Verificar se todas as tags pertencem ao usuário
        const tags = await prisma.tag.findMany({
          where: {
            id: { in: parsedTagIds },
            userId: req.user.id
          }
        })
        if (tags.length !== parsedTagIds.length) {
          return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
        }
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' })
      }
    }

    // Atualizar transação
    const transaction = await prisma.transaction.update({
      where: { id },
      data: {
        description,
        amount: parseFloat(amount),
        type,
        date: date ? new Date(date) : existingTransaction.date,
        categoryId: categoryId || null,
        bankId: bankId || null,
        paymentMethodId: paymentMethodId || null,
        transactionContactId: transactionContactId || null,
        tags: {
          set: parsedTagIds.map(id => ({ id }))
        }
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true,
        tags: true
      }
    })

    res.json(transaction)
  } catch (error) {
    console.error('Erro ao atualizar transação:', error)
    res.status(500).json({ error: 'Erro ao atualizar transação' })
  }
})

// Buscar detalhes das parcelas de uma transação
router.get('/:id/installments', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const transaction = await prisma.transaction.findFirst({
      where: { id, userId },
      include: {
        category: true,
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!transaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    let installments = [];

    // Se a transação tem parcelas (installments > 1)
    if (transaction.installments > 1) {
      // Se é uma transação pai (parentTransactionId é null), buscar todas as parcelas filhas
      if (!transaction.parentTransactionId) {
        installments = await prisma.transaction.findMany({
          where: {
            parentTransactionId: id,
            userId
          },
          orderBy: { currentInstallment: 'asc' },
          include: {
            category: true,
            paymentMethod: {
              include: {
                bank: true
              }
            }
          }
        });
      } else {
        // Se é uma parcela filha, buscar todas as parcelas irmãs
        installments = await prisma.transaction.findMany({
          where: {
            parentTransactionId: transaction.parentTransactionId,
            userId
          },
          orderBy: { currentInstallment: 'asc' },
          include: {
            category: true,
            paymentMethod: {
              include: {
                bank: true
              }
            }
          }
        });
      }
    } else {
      // Se não tem parcelas, retornar apenas a transação atual
      installments = [transaction];
    }

    res.json(installments);
  } catch (error) {
    console.error('Erro ao buscar parcelas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar transação
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    await prisma.transaction.delete({
      where: { id }
    });

    res.json({ message: 'Transação deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
