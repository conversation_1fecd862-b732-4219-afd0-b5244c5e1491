const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const dateService = require('../services/dateService');

const router = express.Router();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Obter informações da data atual
router.get('/info', async (req, res) => {
  try {
    const dateInfo = await dateService.getDateInfo();
    res.json(dateInfo);
  } catch (error) {
    console.error('Erro ao obter informações da data:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Definir data simulada
router.post('/set', async (req, res) => {
  try {
    const { date } = req.body;
    
    if (!date) {
      return res.status(400).json({ error: 'Data é obrigatória' });
    }

    const result = await dateService.setSimulatedDate(date);
    res.json(result);
  } catch (error) {
    console.error('Erro ao definir data simulada:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Remover data simulada (voltar para data real)
router.delete('/clear', async (req, res) => {
  try {
    const result = await dateService.clearSimulatedDate();
    res.json(result);
  } catch (error) {
    console.error('Erro ao remover data simulada:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Avançar data em X dias
router.post('/advance', async (req, res) => {
  try {
    const { days } = req.body;
    
    if (!days || days <= 0) {
      return res.status(400).json({ error: 'Número de dias deve ser maior que 0' });
    }

    const result = await dateService.advanceDate(parseInt(days));
    res.json(result);
  } catch (error) {
    console.error('Erro ao avançar data:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Retroceder data em X dias
router.post('/rewind', async (req, res) => {
  try {
    const { days } = req.body;
    
    if (!days || days <= 0) {
      return res.status(400).json({ error: 'Número de dias deve ser maior que 0' });
    }

    const result = await dateService.rewindDate(parseInt(days));
    res.json(result);
  } catch (error) {
    console.error('Erro ao retroceder data:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Definir data específica com formato amigável
router.post('/set-date', async (req, res) => {
  try {
    const { day, month, year } = req.body;
    
    if (!day || !month || !year) {
      return res.status(400).json({ error: 'Dia, mês e ano são obrigatórios' });
    }

    const date = new Date(year, month - 1, day); // month é 0-indexed
    const result = await dateService.setSimulatedDate(date);
    res.json(result);
  } catch (error) {
    console.error('Erro ao definir data específica:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Ir para o próximo dia de fechamento de um banco
router.post('/goto-billing-day/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;
    
    // Buscar banco
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    if (!bank.billDueDay) {
      return res.status(400).json({ error: 'Banco não tem dia de fechamento configurado' });
    }

    // Calcular próximo dia de fechamento
    const currentDate = await dateService.getCurrentDate();
    const nextBillingDate = new Date(currentDate);
    
    // Se já passou o dia de fechamento deste mês, ir para próximo mês
    if (currentDate.getDate() >= bank.billDueDay) {
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
    }
    
    nextBillingDate.setDate(bank.billDueDay);

    const result = await dateService.setSimulatedDate(nextBillingDate);
    
    res.json({
      ...result,
      bankName: bank.name,
      billingDay: bank.billDueDay,
      message: `Data definida para próximo fechamento do ${bank.name}`
    });
  } catch (error) {
    console.error('Erro ao ir para dia de fechamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atalhos rápidos para testes
router.post('/quick-test/:action', async (req, res) => {
  try {
    const { action } = req.params;
    let result;

    switch (action) {
      case 'tomorrow':
        result = await dateService.advanceDate(1);
        break;
      case 'next-week':
        result = await dateService.advanceDate(7);
        break;
      case 'next-month':
        const currentDate = await dateService.getCurrentDate();
        const nextMonth = new Date(currentDate);
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        result = await dateService.setSimulatedDate(nextMonth);
        break;
      case 'yesterday':
        result = await dateService.rewindDate(1);
        break;
      case 'last-week':
        result = await dateService.rewindDate(7);
        break;
      default:
        return res.status(400).json({ error: 'Ação não reconhecida' });
    }

    res.json(result);
  } catch (error) {
    console.error('Erro no teste rápido:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
