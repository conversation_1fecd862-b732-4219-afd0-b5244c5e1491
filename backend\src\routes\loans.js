const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const { updateContactStatus } = require('./contacts');
const multer = require('multer');
const { v2: cloudinary } = require('cloudinary');

const prisma = new PrismaClient();

// Configurar multer para upload de arquivos
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB
});

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar todos os empréstimos
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { status, type, contactId } = req.query;

    const where = { userId };
    if (status) where.status = status;
    if (type) where.type = type;
    if (contactId) where.contactId = contactId;

    const loans = await prisma.loan.findMany({
      where,
      include: {
        contact: true,
        bank: true,
        payments: {
          orderBy: { dueDate: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(loans);
  } catch (error) {
    console.error('Erro ao buscar empréstimos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar empréstimo por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const loan = await prisma.loan.findFirst({
      where: { id, userId },
      include: {
        contact: true,
        bank: true,
        payments: {
          include: {
            bank: true
          },
          orderBy: { dueDate: 'asc' }
        }
      }
    });

    if (!loan) {
      return res.status(404).json({ error: 'Empréstimo não encontrado' });
    }

    res.json(loan);
  } catch (error) {
    console.error('Erro ao buscar empréstimo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo empréstimo
router.post('/', upload.single('receipt'), async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      contactId,
      title,
      type,
      loanType,
      totalAmount,
      installments,
      interestRate,
      startDate,
      expectedEndDate,
      bankId,
      notes
    } = req.body;

    // Validar campos obrigatórios
    if (!contactId || !title || !type || !loanType || !totalAmount || !installments || !startDate || !bankId) {
      return res.status(400).json({
        error: 'Campos obrigatórios faltando',
        required: ['contactId', 'title', 'type', 'loanType', 'totalAmount', 'installments', 'startDate', 'bankId']
      });
    }

    let receiptUrl = null;

    // Upload do comprovante se fornecido
    if (req.file) {
      try {
        const result = await new Promise((resolve, reject) => {
          cloudinary.uploader.upload_stream(
            {
              resource_type: 'auto',
              folder: 'loan_receipts'
            },
            (error, result) => {
              if (error) reject(error);
              else resolve(result);
            }
          ).end(req.file.buffer);
        });

        receiptUrl = result.secure_url;
      } catch (uploadError) {
        console.error('Erro no upload do comprovante:', uploadError);
      }
    }

    // Calcular valor da parcela com arredondamento
    const rawInstallmentAmount = installments > 1 ?
      (parseFloat(totalAmount) * (1 + parseFloat(interestRate) / 100)) / parseInt(installments) :
      parseFloat(totalAmount);

    // Arredondar para 2 casas decimais
    const installmentAmount = Math.round(rawInstallmentAmount * 100) / 100;

    const loan = await prisma.loan.create({
      data: {
        contactId,
        title,
        type,
        loanType,
        totalAmount: parseFloat(totalAmount),
        installmentAmount,
        installments: parseInt(installments),
        interestRate: parseFloat(interestRate),
        startDate: new Date(startDate),
        expectedEndDate: expectedEndDate ? new Date(expectedEndDate) : null,
        bankId,
        receiptUrl,
        notes: notes || null,
        userId
      }
    });

    // Criar parcelas de pagamento
    const payments = [];
    const startDateObj = new Date(startDate);

    for (let i = 0; i < parseInt(installments); i++) {
      const dueDate = new Date(startDateObj);
      dueDate.setMonth(dueDate.getMonth() + i);

      payments.push({
        loanId: loan.id,
        amount: installmentAmount,
        dueDate,
        installmentNumber: i + 1,
        userId
      });
    }

    await prisma.loanPayment.createMany({
      data: payments
    });

    // Buscar nome do contato para a descrição
    const contact = await prisma.contact.findUnique({
      where: { id: contactId }
    });

    // Criar transação inicial (banco é obrigatório)
    const transactionDescription = type === 'LOAN_GIVEN' ?
      `Empréstimo para ${contact?.name || 'Contato'}` :
      `Empréstimo de ${contact?.name || 'Contato'}`;

    console.log('Criando transação inicial:', {
      description: transactionDescription,
      amount: parseFloat(totalAmount),
      type: type === 'LOAN_GIVEN' ? 'EXPENSE' : 'INCOME',
      bankId,
      userId,
      date: new Date(startDate)
    });

    const transaction = await prisma.transaction.create({
      data: {
        description: transactionDescription,
        amount: parseFloat(totalAmount),
        type: type === 'LOAN_GIVEN' ? 'EXPENSE' : 'INCOME',
        bankId,
        userId,
        date: new Date(startDate)
      }
    });

    console.log('Transação criada:', transaction);

    // Atualizar saldo do banco
    const bank = await prisma.bank.findUnique({ where: { id: bankId } });
    if (bank) {
      const newBalance = type === 'LOAN_GIVEN' ?
        bank.currentBalance - parseFloat(totalAmount) :
        bank.currentBalance + parseFloat(totalAmount);

      await prisma.bank.update({
        where: { id: bankId },
        data: { currentBalance: newBalance }
      });
    }

    // Buscar empréstimo criado com relações
    const createdLoan = await prisma.loan.findUnique({
      where: { id: loan.id },
      include: {
        contact: true,
        bank: true,
        payments: {
          orderBy: { dueDate: 'asc' }
        }
      }
    });

    res.status(201).json(createdLoan);
  } catch (error) {
    console.error('Erro ao criar empréstimo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Marcar empréstimo como completo
router.patch('/:id/complete', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Buscar empréstimo
    const loan = await prisma.loan.findFirst({
      where: { id, userId },
      include: {
        payments: true,
        contact: true
      }
    });

    if (!loan) {
      return res.status(404).json({ error: 'Empréstimo não encontrado' });
    }

    // Marcar todas as parcelas como pagas
    const unpaidPayments = loan.payments.filter(p => !p.isPaid);

    for (const payment of unpaidPayments) {
      await prisma.loanPayment.update({
        where: { id: payment.id },
        data: {
          isPaid: true,
          paymentDate: new Date(),
          amount: payment.amount
        }
      });

      // Criar transação para cada parcela paga
      if (loan.bankId) {
        const paymentDescription = loan.type === 'LOAN_GIVEN' ?
          `Recebimento de ${loan.contact?.name || 'Contato'} - ${loan.title} (${payment.installmentNumber}/${loan.installments})` :
          `Pagamento para ${loan.contact?.name || 'Contato'} - ${loan.title} (${payment.installmentNumber}/${loan.installments})`;

        await prisma.transaction.create({
          data: {
            description: paymentDescription,
            amount: payment.amount,
            type: loan.type === 'LOAN_GIVEN' ? 'INCOME' : 'EXPENSE',
            bankId: loan.bankId,
            userId,
            date: new Date()
          }
        });

        // Atualizar saldo do banco
        const bank = await prisma.bank.findUnique({ where: { id: loan.bankId } });
        if (bank) {
          const newBalance = loan.type === 'LOAN_GIVEN' ?
            bank.currentBalance + payment.amount :
            bank.currentBalance - payment.amount;

          await prisma.bank.update({
            where: { id: loan.bankId },
            data: { currentBalance: newBalance }
          });
        }
      }
    }

    // Marcar empréstimo como completo
    await prisma.loan.update({
      where: { id },
      data: {
        status: 'COMPLETED',
        paidInstallments: loan.installments // Atualizar parcelas pagas para o total
      }
    });

    res.json({ message: 'Empréstimo marcado como quitado com sucesso' });
  } catch (error) {
    console.error('Erro ao marcar empréstimo como completo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Registrar pagamento de parcela
router.post('/:id/payment', upload.single('receipt'), async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { paymentId, amount, paymentDate, bankId, notes } = req.body;

    // Verificar se o empréstimo pertence ao usuário
    const loan = await prisma.loan.findFirst({
      where: { id, userId },
      include: { contact: true }
    });

    if (!loan) {
      return res.status(404).json({ error: 'Empréstimo não encontrado' });
    }

    // Buscar a parcela específica
    const payment = await prisma.loanPayment.findFirst({
      where: { id: paymentId, loanId: id }
    });

    if (!payment) {
      return res.status(404).json({ error: 'Parcela não encontrada' });
    }

    if (payment.isPaid) {
      return res.status(400).json({ error: 'Parcela já foi paga' });
    }

    let receiptUrl = payment.receiptUrl;

    // Upload do comprovante se fornecido
    if (req.file) {
      try {
        const result = await new Promise((resolve, reject) => {
          cloudinary.uploader.upload_stream(
            {
              resource_type: 'auto',
              folder: 'payment_receipts'
            },
            (error, result) => {
              if (error) reject(error);
              else resolve(result);
            }
          ).end(req.file.buffer);
        });

        receiptUrl = result.secure_url;
      } catch (uploadError) {
        console.error('Erro no upload do comprovante:', uploadError);
      }
    }

    // Verificar se o pagamento está atrasado
    const isLate = new Date(paymentDate) > payment.dueDate;

    // Verificar se o valor pago é diferente do valor original da parcela
    const paidAmount = parseFloat(amount);
    const originalAmount = payment.amount;
    const difference = originalAmount - paidAmount;

    // Atualizar a parcela
    const updatedPayment = await prisma.loanPayment.update({
      where: { id: paymentId },
      data: {
        isPaid: true,
        isLate,
        paymentDate: new Date(paymentDate),
        bankId: bankId || null,
        receiptUrl,
        notes: notes || null,
        amount: paidAmount
      }
    });

    // Se houve diferença no valor, redistribuir nas parcelas não pagas
    if (difference !== 0) {
      const unpaidPayments = await prisma.loanPayment.findMany({
        where: {
          loanId: id,
          isPaid: false,
          id: { not: paymentId }
        }
      });

      if (unpaidPayments.length > 0) {
        const redistributeAmount = difference / unpaidPayments.length;
        const roundedRedistributeAmount = Math.round(redistributeAmount * 100) / 100;

        for (const unpaidPayment of unpaidPayments) {
          const newAmount = unpaidPayment.amount + roundedRedistributeAmount;
          const roundedNewAmount = Math.round(newAmount * 100) / 100;

          await prisma.loanPayment.update({
            where: { id: unpaidPayment.id },
            data: { amount: roundedNewAmount }
          });
        }
      }
    }

    // Atualizar contador de parcelas pagas no empréstimo
    const paidCount = await prisma.loanPayment.count({
      where: { loanId: id, isPaid: true }
    });

    const updatedLoan = await prisma.loan.update({
      where: { id },
      data: {
        paidInstallments: paidCount,
        status: paidCount === loan.installments ? 'COMPLETED' : 'ACTIVE'
      }
    });

    // Criar transação se houver banco
    if (bankId) {
      const paymentDescription = loan.type === 'LOAN_GIVEN' ?
        `Recebimento de ${loan.contact?.name || 'Contato'} - ${loan.title} (${paidCount}/${loan.installments})` :
        `Pagamento para ${loan.contact?.name || 'Contato'} - ${loan.title} (${paidCount}/${loan.installments})`;

      console.log('Criando transação de pagamento:', {
        description: paymentDescription,
        amount: parseFloat(amount),
        type: loan.type === 'LOAN_GIVEN' ? 'INCOME' : 'EXPENSE',
        bankId,
        userId,
        date: new Date(paymentDate)
      });

      const paymentTransaction = await prisma.transaction.create({
        data: {
          description: paymentDescription,
          amount: parseFloat(amount),
          type: loan.type === 'LOAN_GIVEN' ? 'INCOME' : 'EXPENSE',
          bankId,
          userId,
          date: new Date(paymentDate)
        }
      });

      console.log('Transação de pagamento criada:', paymentTransaction);

      // Atualizar saldo do banco
      const bank = await prisma.bank.findUnique({ where: { id: bankId } });
      if (bank) {
        const newBalance = loan.type === 'LOAN_GIVEN' ?
          bank.currentBalance + parseFloat(amount) :
          bank.currentBalance - parseFloat(amount);

        await prisma.bank.update({
          where: { id: bankId },
          data: { currentBalance: newBalance }
        });
      }
    }

    // Atualizar status do contato
    await updateContactStatus(loan.contactId);

    res.json(updatedPayment);
  } catch (error) {
    console.error('Erro ao registrar pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar empréstimo
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Buscar empréstimo com transações relacionadas
    const loan = await prisma.loan.findFirst({
      where: { id, userId },
      include: {
        payments: true,
        bank: true
      }
    });

    if (!loan) {
      return res.status(404).json({ error: 'Empréstimo não encontrado' });
    }

    // Deletar transações relacionadas ao empréstimo
    await prisma.transaction.deleteMany({
      where: {
        userId,
        OR: [
          { description: { contains: loan.title } },
          { description: { startsWith: `Empréstimo para ${loan.title}` } },
          { description: { startsWith: `Empréstimo de ${loan.title}` } },
          { description: { startsWith: `Pagamento: ${loan.title}` } }
        ]
      }
    });

    // Reverter saldo do banco se necessário
    if (loan.bankId && loan.bank) {
      // Calcular quanto foi pago até agora
      const paidAmount = loan.payments
        .filter(p => p.isPaid)
        .reduce((sum, p) => sum + p.amount, 0);

      // Reverter transação inicial
      const initialReversion = loan.type === 'LOAN_GIVEN' ?
        loan.bank.currentBalance + loan.totalAmount :
        loan.bank.currentBalance - loan.totalAmount;

      // Reverter pagamentos recebidos
      const finalBalance = loan.type === 'LOAN_GIVEN' ?
        initialReversion - paidAmount :
        initialReversion + paidAmount;

      await prisma.bank.update({
        where: { id: loan.bankId },
        data: { currentBalance: finalBalance }
      });
    }

    // Deletar parcelas de pagamento
    await prisma.loanPayment.deleteMany({
      where: { loanId: id }
    });

    // Deletar empréstimo
    await prisma.loan.delete({
      where: { id }
    });

    // Atualizar status do contato
    await updateContactStatus(loan.contactId);

    res.json({ message: 'Empréstimo deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar empréstimo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar valor da parcela
router.patch('/:loanId/payments/:paymentId/amount', async (req, res) => {
  try {
    const { loanId, paymentId } = req.params;
    const { amount } = req.body;
    const userId = req.user.id;

    // Verificar se o empréstimo pertence ao usuário
    const loan = await prisma.loan.findFirst({
      where: { id: loanId, userId },
      include: { payments: true }
    });

    if (!loan) {
      return res.status(404).json({ error: 'Empréstimo não encontrado' });
    }

    // Verificar se a parcela existe
    const payment = await prisma.loanPayment.findFirst({
      where: { id: paymentId, loanId }
    });

    if (!payment) {
      return res.status(404).json({ error: 'Parcela não encontrada' });
    }

    if (payment.isPaid) {
      return res.status(400).json({ error: 'Não é possível alterar parcela já paga' });
    }

    // Calcular diferença e redistribuir
    const oldAmount = payment.amount;
    const newAmount = parseFloat(amount);
    const difference = oldAmount - newAmount;

    // Atualizar a parcela atual
    await prisma.loanPayment.update({
      where: { id: paymentId },
      data: { amount: newAmount }
    });

    // Redistribuir diferença nas parcelas não pagas
    if (difference !== 0) {
      const unpaidPayments = loan.payments.filter(p => !p.isPaid && p.id !== paymentId);

      if (unpaidPayments.length > 0) {
        const redistributeAmount = difference / unpaidPayments.length;
        const roundedRedistributeAmount = Math.round(redistributeAmount * 100) / 100;

        for (const unpaidPayment of unpaidPayments) {
          const newAmount = unpaidPayment.amount + roundedRedistributeAmount;
          const roundedNewAmount = Math.round(newAmount * 100) / 100;

          await prisma.loanPayment.update({
            where: { id: unpaidPayment.id },
            data: { amount: roundedNewAmount }
          });
        }
      }
    }

    res.json({ message: 'Valor da parcela atualizado com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar valor da parcela:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar data de vencimento da parcela
router.patch('/:loanId/payments/:paymentId/due-date', async (req, res) => {
  try {
    const { loanId, paymentId } = req.params;
    const { dueDate } = req.body;
    const userId = req.user.id;

    // Verificar se o empréstimo pertence ao usuário
    const loan = await prisma.loan.findFirst({
      where: { id: loanId, userId }
    });

    if (!loan) {
      return res.status(404).json({ error: 'Empréstimo não encontrado' });
    }

    // Verificar se a parcela existe
    const payment = await prisma.loanPayment.findFirst({
      where: { id: paymentId, loanId }
    });

    if (!payment) {
      return res.status(404).json({ error: 'Parcela não encontrada' });
    }

    // Atualizar data de vencimento
    await prisma.loanPayment.update({
      where: { id: paymentId },
      data: { dueDate: new Date(dueDate) }
    });

    res.json({ message: 'Data de vencimento atualizada com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar data de vencimento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar parcela
router.delete('/:loanId/payments/:paymentId', async (req, res) => {
  try {
    const { loanId, paymentId } = req.params;
    const userId = req.user.id;

    // Verificar se o empréstimo pertence ao usuário
    const loan = await prisma.loan.findFirst({
      where: { id: loanId, userId },
      include: { payments: true }
    });

    if (!loan) {
      return res.status(404).json({ error: 'Empréstimo não encontrado' });
    }

    // Verificar se a parcela existe
    const payment = await prisma.loanPayment.findFirst({
      where: { id: paymentId, loanId }
    });

    if (!payment) {
      return res.status(404).json({ error: 'Parcela não encontrada' });
    }

    if (payment.isPaid) {
      return res.status(400).json({ error: 'Não é possível deletar parcela já paga' });
    }

    // Redistribuir valor da parcela deletada
    const unpaidPayments = loan.payments.filter(p => !p.isPaid && p.id !== paymentId);

    if (unpaidPayments.length > 0) {
      const redistributeAmount = payment.amount / unpaidPayments.length;

      for (const unpaidPayment of unpaidPayments) {
        await prisma.loanPayment.update({
          where: { id: unpaidPayment.id },
          data: { amount: unpaidPayment.amount + redistributeAmount }
        });
      }
    }

    // Deletar a parcela
    await prisma.loanPayment.delete({
      where: { id: paymentId }
    });

    // Atualizar número total de parcelas do empréstimo
    const remainingPayments = await prisma.loanPayment.count({
      where: { loanId }
    });

    await prisma.loan.update({
      where: { id: loanId },
      data: { installments: remainingPayments }
    });

    res.json({ message: 'Parcela deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar parcela:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
