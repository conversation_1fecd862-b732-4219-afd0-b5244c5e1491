import React from 'react'
import { Tag, X } from 'lucide-react'

function TagSelector({ tags, selectedTags, onTagToggle, label = "Tags" }) {
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} (Opcional)
      </label>

      {tags.length === 0 ? (
        <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
          <Tag className="h-8 w-8 mx-auto mb-2 text-gray-300" />
          <p className="text-sm">Nenhuma tag cadastrada</p>
        </div>
      ) : (
        <div className="border border-gray-300 rounded-lg p-4 bg-white">
          {/* Tags Selecionadas */}
          {selectedTags.length > 0 && (
            <div className="mb-4 pb-3 border-b border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                  Selecionadas ({selectedTags.length})
                </span>
                <button
                  type="button"
                  onClick={() => selectedTags.forEach(tagId => onTagToggle(tagId))}
                  className="text-xs text-red-600 hover:text-red-700 font-medium"
                >
                  Limpar todas
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedTags.map(tagId => {
                  const tag = tags.find(t => t.id === tagId)
                  if (!tag) return null
                  return (
                    <span
                      key={tag.id}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200"
                    >
                      <Tag className="h-3 w-3" />
                      {tag.name}
                      <button
                        type="button"
                        onClick={() => onTagToggle(tag.id)}
                        className="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  )
                })}
              </div>
            </div>
          )}

          {/* Tags Disponíveis */}
          <div>
            <span className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-3 block">
              Disponíveis
            </span>
            <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
              {tags
                .filter(tag => !selectedTags.includes(tag.id))
                .map((tag) => (
                  <button
                    key={tag.id}
                    type="button"
                    onClick={() => onTagToggle(tag.id)}
                    className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full border border-gray-200 hover:bg-gray-200 hover:border-gray-300 transition-all duration-200"
                  >
                    <Tag className="h-3 w-3" />
                    {tag.name}
                  </button>
                ))}
              {tags.filter(tag => !selectedTags.includes(tag.id)).length === 0 && (
                <span className="text-sm text-gray-500 italic">
                  Todas as tags foram selecionadas
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TagSelector
