import React from 'react'
import { Tag, Check } from 'lucide-react'

function TagSelector({ tags, selectedTags, onTagToggle, label = "Tags" }) {
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} (Opcional)
      </label>
      
      {tags.length === 0 ? (
        <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
          <Tag className="h-8 w-8 mx-auto mb-2 text-gray-300" />
          <p className="text-sm">Nenhuma tag cadastrada</p>
        </div>
      ) : (
        <div className="border border-gray-300 rounded-lg p-4 bg-gray-50 max-h-48 overflow-y-auto">
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            {tags.map((tag) => {
              const isSelected = selectedTags.includes(tag.id)
              return (
                <button
                  key={tag.id}
                  type="button"
                  onClick={() => onTagToggle(tag.id)}
                  className={`relative flex items-center gap-2 p-3 rounded-lg border-2 transition-all duration-200 text-left ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className={`flex-shrink-0 w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    isSelected
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {isSelected && (
                      <Check className="h-3 w-3 text-white" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-1">
                      <Tag className="h-3 w-3 flex-shrink-0" />
                      <span className="text-sm font-medium truncate">
                        {tag.name}
                      </span>
                    </div>
                    {tag.description && (
                      <p className="text-xs text-gray-500 truncate mt-1">
                        {tag.description}
                      </p>
                    )}
                  </div>
                </button>
              )
            })}
          </div>
          
          {selectedTags.length > 0 && (
            <div className="mt-4 pt-3 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">
                  {selectedTags.length} tag(s) selecionada(s)
                </span>
                <button
                  type="button"
                  onClick={() => selectedTags.forEach(tagId => onTagToggle(tagId))}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Limpar seleção
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default TagSelector
