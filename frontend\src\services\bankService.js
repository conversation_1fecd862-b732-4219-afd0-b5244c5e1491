import api from './api'

export const bankService = {
  // Listar bancos
  async getBanks() {
    const response = await api.get('/banks')
    return response.data
  },

  // Criar banco
  async createBank(bankData) {
    const response = await api.post('/banks', bankData)
    return response.data
  },

  // Atualizar banco
  async updateBank(id, bankData) {
    const response = await api.put(`/banks/${id}`, bankData)
    return response.data
  },

  // Deletar banco
  async deleteBank(id) {
    const response = await api.delete(`/banks/${id}`)
    return response.data
  },

  // Obter saldo total
  async getTotalBalance() {
    const response = await api.get('/banks/balance')
    return response.data
  },

  // Transferir entre bancos
  async transferBetweenBanks(transferData) {
    const response = await api.post('/banks/transfer', transferData)
    return response.data
  },

  // Pagar fatura do banco
  async payBill(bankId, paymentData) {
    const response = await api.post(`/banks/${bankId}/pay-bill`, paymentData)
    return response.data
  }

  // Buscar opções de pagamento
  async getPaymentOptions() {
    const response = await api.get('/banks/payment-options')
    return response.data
  }
}

export const paymentMethodService = {
  // Listar formas de pagamento
  async getPaymentMethods() {
    const response = await api.get('/payment-methods')
    return response.data
  },

  // Criar forma de pagamento
  async createPaymentMethod(methodData) {
    const response = await api.post('/payment-methods', methodData)
    return response.data
  },

  // Atualizar forma de pagamento
  async updatePaymentMethod(id, methodData) {
    const response = await api.put(`/payment-methods/${id}`, methodData)
    return response.data
  },

  // Deletar forma de pagamento
  async deletePaymentMethod(id) {
    const response = await api.delete(`/payment-methods/${id}`)
    return response.data
  },

  // Criar formas de pagamento padrão
  async createDefaults() {
    const response = await api.post('/payment-methods/create-defaults')
    return response.data
  },

  // Pagar fatura de cartão de crédito
  async payBill(id) {
    const response = await api.post(`/payment-methods/${id}/pay-bill`)
    return response.data
  }
}

export const savingsService = {
  // Listar cofrinhos
  async getSavings() {
    const response = await api.get('/savings')
    return response.data
  },

  // Criar cofrinho
  async createSavings(savingsData) {
    const response = await api.post('/savings', savingsData)
    return response.data
  },

  // Atualizar cofrinho
  async updateSavings(id, savingsData) {
    const response = await api.put(`/savings/${id}`, savingsData)
    return response.data
  },

  // Deletar cofrinho
  async deleteSavings(id) {
    const response = await api.delete(`/savings/${id}`)
    return response.data
  },

  // Depositar no cofrinho
  async deposit(id, amount, bankId) {
    const response = await api.post(`/savings/${id}/deposit`, { amount, bankId })
    return response.data
  },

  // Sacar do cofrinho
  async withdraw(id, amount, bankId) {
    const response = await api.post(`/savings/${id}/withdraw`, { amount, bankId })
    return response.data
  }
}

export const subscriptionService = {
  // Listar assinaturas
  async getSubscriptions() {
    const response = await api.get('/subscriptions')
    return response.data
  },

  // Criar assinatura
  async createSubscription(subscriptionData) {
    const response = await api.post('/subscriptions', subscriptionData)
    return response.data
  },

  // Atualizar assinatura
  async updateSubscription(id, subscriptionData) {
    const response = await api.put(`/subscriptions/${id}`, subscriptionData)
    return response.data
  },

  // Deletar assinatura
  async deleteSubscription(id) {
    const response = await api.delete(`/subscriptions/${id}`)
    return response.data
  },

  // Processar faturas de assinaturas
  async processBills() {
    const response = await api.post('/subscriptions/process-bills')
    return response.data
  }
}
