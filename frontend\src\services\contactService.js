import api from './api'

// Serviço para contatos de transações (nova tabela)
export const transactionContactservice = {
  // Listar todos os contatos
  async getContacts() {
    const response = await api.get('/transactionContacts')
    return response.data
  },

  // Buscar contato por ID
  async getContactById(id) {
    const response = await api.get(`/transactionContacts/${id}`)
    return response.data
  },

  // Criar contato
  async createContact(contactData) {
    const response = await api.post('/transactionContacts', contactData)
    return response.data
  },

  // Atualizar contato
  async updateContact(id, contactData) {
    const response = await api.put(`/transactionContacts/${id}`, contactData)
    return response.data
  },

  // Deletar contato
  async deleteContact(id) {
    const response = await api.delete(`/transactionContacts/${id}`)
    return response.data
  },

  // Upload de foto do contato
  async uploadContactPhoto(id, photoFile) {
    const formData = new FormData()
    formData.append('photo', photoFile)

    const response = await api.post(`/transactionContacts/${id}/photo`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  // Buscar estatísticas do contato
  async getContactStats(id, month, year) {
    const response = await api.get(`/transactionContacts/${id}/stats`, {
      params: { month, year }
    })
    return response.data
  }
}

// Serviço para contatos de empréstimos (tabela existente)
export const loanContactService = {
  // Listar todos os contatos
  async getContacts() {
    const response = await api.get('/contacts')
    return response.data
  },

  // Buscar contato por ID
  async getContactById(id) {
    const response = await api.get(`/contacts/${id}`)
    return response.data
  },

  // Criar contato
  async createContact(contactData) {
    const response = await api.post('/contacts', contactData)
    return response.data
  },

  // Atualizar contato
  async updateContact(id, contactData) {
    const response = await api.put(`/contacts/${id}`, contactData)
    return response.data
  },

  // Deletar contato
  async deleteContact(id) {
    const response = await api.delete(`/contacts/${id}`)
    return response.data
  },

  // Upload de foto do contato
  async uploadContactPhoto(id, photoFile) {
    const formData = new FormData()
    formData.append('photo', photoFile)

    const response = await api.post(`/contacts/${id}/photo`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }
}

// Manter compatibilidade com código existente
export const contactService = loanContactService
