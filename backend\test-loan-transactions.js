const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testLoanTransactions() {
  try {
    console.log('🔍 VERIFICANDO TRANSAÇÕES DE EMPRÉSTIMOS...\n');

    // Buscar todos os empréstimos
    const loans = await prisma.loan.findMany({
      include: {
        contact: true,
        bank: true,
        payments: true
      }
    });

    console.log(`📊 Total de empréstimos encontrados: ${loans.length}\n`);

    for (const loan of loans) {
      console.log(`\n🏷️  EMPRÉSTIMO: ${loan.title}`);
      console.log(`   👤 Contato: ${loan.contact?.name || 'N/A'}`);
      console.log(`   💰 Valor: R$ ${loan.totalAmount}`);
      console.log(`   📝 Tipo: ${loan.type}`);
      console.log(`   🏦 Banco: ${loan.bank?.name || 'N/A'}`);
      console.log(`   📅 Data: ${loan.startDate}`);

      // Buscar transações relacionadas a este empréstimo
      const transactions = await prisma.transaction.findMany({
        where: {
          OR: [
            { description: { contains: loan.title } },
            { description: { contains: loan.contact?.name || '' } }
          ]
        },
        include: {
          bank: true
        }
      });

      console.log(`   🔄 Transações encontradas: ${transactions.length}`);
      
      if (transactions.length === 0) {
        console.log('   ❌ PROBLEMA: Nenhuma transação encontrada para este empréstimo!');
      } else {
        transactions.forEach((transaction, index) => {
          console.log(`     ${index + 1}. ${transaction.description}`);
          console.log(`        💵 Valor: R$ ${transaction.amount}`);
          console.log(`        📊 Tipo: ${transaction.type}`);
          console.log(`        🏦 Banco: ${transaction.bank?.name || 'N/A'}`);
          console.log(`        📅 Data: ${transaction.date}`);
        });
      }

      // Verificar parcelas pagas
      const paidPayments = loan.payments.filter(p => p.isPaid);
      console.log(`   💳 Parcelas pagas: ${paidPayments.length}/${loan.payments.length}`);
      
      if (paidPayments.length > 0) {
        console.log('   📋 Parcelas pagas:');
        paidPayments.forEach(payment => {
          console.log(`     - Parcela ${payment.installmentNumber}: R$ ${payment.amount} (${payment.paymentDate})`);
        });
      }
    }

    // Verificar saldos dos bancos
    console.log('\n\n🏦 SALDOS DOS BANCOS:');
    const banks = await prisma.bank.findMany();
    banks.forEach(bank => {
      console.log(`   ${bank.name}: R$ ${bank.currentBalance}`);
    });

    // Verificar últimas transações
    console.log('\n\n📋 ÚLTIMAS 10 TRANSAÇÕES:');
    const recentTransactions = await prisma.transaction.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        bank: true
      }
    });

    recentTransactions.forEach((transaction, index) => {
      console.log(`   ${index + 1}. ${transaction.description}`);
      console.log(`      💵 R$ ${transaction.amount} (${transaction.type})`);
      console.log(`      🏦 ${transaction.bank?.name || 'N/A'}`);
      console.log(`      📅 ${transaction.date}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Erro ao verificar transações:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLoanTransactions();
