import React from 'react'
import { Settings as SettingsIcon, User, Bell, Shield, Database, Palette, Globe } from 'lucide-react'
import DateSimulator from '../components/DateSimulator'

function Settings() {
  return (
    <div className="space-y-8 fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-6 border border-gray-100">
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-gray-500 to-slate-600 rounded-xl flex items-center justify-center mr-4">
            <SettingsIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Configurações</h1>
            <p className="text-sm text-gray-600">Personalize e configure seu sistema financeiro</p>
          </div>
        </div>
      </div>

      {/* Simulador de Data para Testes */}
      <DateSimulator />

      {/* Seções de Configuração */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        {/* Perfil do Usuário */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <User className="h-5 w-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Perfil do Usuário</h2>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Gerencie suas informações pessoais e preferências de conta.
          </p>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Nome de usuário</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Email</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
          </div>
        </div>

        {/* Notificações */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <Bell className="h-5 w-5 text-yellow-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Notificações</h2>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Configure alertas e lembretes financeiros.
          </p>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Vencimentos</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Metas</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
          </div>
        </div>

        {/* Segurança */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <Shield className="h-5 w-5 text-green-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Segurança</h2>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Gerencie senhas e configurações de segurança.
          </p>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Alterar senha</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Autenticação 2FA</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
          </div>
        </div>

        {/* Dados e Backup */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <Database className="h-5 w-5 text-purple-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Dados e Backup</h2>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Exporte, importe e faça backup dos seus dados.
          </p>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Exportar dados</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Backup automático</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
          </div>
        </div>

        {/* Aparência */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <Palette className="h-5 w-5 text-pink-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Aparência</h2>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Personalize cores, temas e layout do sistema.
          </p>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Tema escuro/claro</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Cores personalizadas</p>
              <p className="font-medium text-gray-900">Em desenvolvimento...</p>
            </div>
          </div>
        </div>

        {/* Idioma e Região */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <Globe className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Idioma e Região</h2>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Configure idioma, moeda e formato de data.
          </p>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Idioma</p>
              <p className="font-medium text-gray-900">Português (Brasil)</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Moeda</p>
              <p className="font-medium text-gray-900">Real (BRL)</p>
            </div>
          </div>
        </div>

      </div>

      {/* Aviso de Desenvolvimento */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
            <SettingsIcon className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-1">Tela em Desenvolvimento</h3>
            <p className="text-blue-700 text-sm">
              Esta tela de configurações está sendo desenvolvida. Em breve você poderá personalizar 
              completamente seu sistema financeiro com todas as opções listadas acima.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
