const { PrismaClient } = require('@prisma/client');
const dateService = require('./dateService');
const billService = require('./billService');
const { BILL_STATUS, INSTALLMENT_STATUS, SUBSCRIPTION_STATUS } = require('../constants/status');

const prisma = new PrismaClient();

class DailyBillingService {
  constructor() {
    this.isRunning = false;
    this.lastRunDate = null;
    this.intervalId = null;
  }

  /**
   * Inicia o agendamento diário
   */
  start() {
    console.log('🕐 Iniciando serviço de processamento diário de faturas...');
    
    // Executar imediatamente se ainda não rodou hoje
    this.checkAndRun();
    
    // Agendar para rodar a cada hora (verificará se já rodou hoje)
    this.intervalId = setInterval(() => {
      this.checkAndRun();
    }, 60 * 60 * 1000); // A cada 1 hora
    
    console.log('✅ Serviço de processamento diário iniciado');
  }

  /**
   * Para o agendamento
   */
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('🛑 Serviço de processamento diário parado');
    }
  }

  /**
   * Verifica se deve executar e executa se necessário
   */
  async checkAndRun() {
    try {
      const currentDate = await dateService.getCurrentDate();
      const today = currentDate.toDateString();
      
      // Verificar se já rodou hoje
      if (this.lastRunDate === today) {
        return;
      }

      // Verificar se é após 00:00 ou se nunca rodou
      const currentHour = currentDate.getHours();
      if (currentHour >= 0 || this.lastRunDate === null) {
        console.log(`🌅 Executando processamento diário - ${currentDate.toLocaleString()}`);
        await this.processDailyBilling();
        this.lastRunDate = today;
      }
    } catch (error) {
      console.error('❌ Erro no agendamento diário:', error);
    }
  }

  /**
   * MÉTODO PRINCIPAL: Processamento diário de faturas
   */
  async processDailyBilling() {
    if (this.isRunning) {
      console.log('⏳ Processamento diário já está em execução...');
      return;
    }

    this.isRunning = true;
    
    try {
      console.log('🔄 Iniciando processamento diário de faturas...');
      
      const currentDate = await dateService.getCurrentDate();
      console.log(`📅 Data atual: ${currentDate.toLocaleDateString()}`);

      // Buscar todos os bancos com limite de crédito
      const banksWithCredit = await prisma.bank.findMany({
        where: {
          creditLimit: {
            gt: 0
          },
          billDueDay: {
            not: null
          }
        },
        include: {
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      console.log(`🏦 Encontrados ${banksWithCredit.length} bancos com limite de crédito`);

      let processedBanks = 0;
      let overdueCount = 0;
      let paidCount = 0;
      let newBillsCount = 0;

      for (const bank of banksWithCredit) {
        try {
          console.log(`\n🏦 Processando banco: ${bank.name} (ID: ${bank.id})`);
          
          const result = await this.processBankBilling(bank, currentDate);
          
          if (result.becameOverdue) overdueCount++;
          if (result.becamePaid) paidCount++;
          if (result.newBillCreated) newBillsCount++;
          
          processedBanks++;
          
        } catch (error) {
          console.error(`❌ Erro ao processar banco ${bank.name}:`, error);
        }
      }

      console.log('\n📊 RESUMO DO PROCESSAMENTO DIÁRIO:');
      console.log(`   🏦 Bancos processados: ${processedBanks}`);
      console.log(`   🔴 Faturas em atraso: ${overdueCount}`);
      console.log(`   ✅ Faturas pagas automaticamente: ${paidCount}`);
      console.log(`   🆕 Novas faturas criadas: ${newBillsCount}`);
      console.log('✅ Processamento diário concluído com sucesso!');

      return {
        success: true,
        processedBanks,
        overdueCount,
        paidCount,
        newBillsCount
      };

    } catch (error) {
      console.error('❌ Erro no processamento diário:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Processa a faturação de um banco específico
   */
  async processBankBilling(bank, currentDate) {
    const result = {
      becameOverdue: false,
      becamePaid: false,
      newBillCreated: false
    };

    // Calcular data de vencimento atual
    const billDueDate = bank.billDueDate || this.calculateBillDueDate(bank.billDueDay, currentDate);
    
    // Calcular valor atual da fatura
    const currentBillAmount = await billService.calculateCurrentBillAmount(bank.id);
    
    console.log(`   📅 Vencimento: ${billDueDate.toLocaleDateString()}`);
    console.log(`   💰 Valor da fatura: R$ ${currentBillAmount}`);
    console.log(`   📊 Status atual: ${bank.billStatus}`);

    // Verificar se a data atual é maior que o vencimento
    if (currentDate > billDueDate && bank.billStatus === BILL_STATUS.PENDING) {
      
      // REGRA 1: Fatura vencida com valor > 0 = OVERDUE
      if (currentBillAmount > 0) {
        await prisma.bank.update({
          where: { id: bank.id },
          data: {
            billStatus: BILL_STATUS.OVERDUE,
            currentBillAmount
          }
        });
        
        console.log(`   🔴 Fatura marcada como OVERDUE: R$ ${currentBillAmount}`);
        result.becameOverdue = true;
      }
      // REGRA 2: Fatura vencida com valor <= 0 = PAID + Nova fatura
      else {
        await this.processZeroBill(bank, billDueDate, currentDate);
        
        console.log(`   ✅ Fatura zerada processada - Nova fatura criada`);
        result.becamePaid = true;
        result.newBillCreated = true;
      }
    }

    // Processar assinaturas reservadas independentemente
    await this.processReservedSubscriptions(bank.id, currentDate);

    return result;
  }

  /**
   * Processa fatura com valor zero (REGRA 2)
   */
  async processZeroBill(bank, billDueDate, currentDate) {
    await prisma.$transaction(async (tx) => {
      // Salvar no histórico
      await tx.billHistory.create({
        data: {
          bankId: bank.id,
          amount: 0,
          dueDate: billDueDate,
          paidDate: currentDate,
          paymentBankId: bank.id, // Auto-pagamento
          userId: bank.userId
        }
      });

      // Calcular próxima data de vencimento
      const nextBillDueDate = new Date(billDueDate);
      nextBillDueDate.setMonth(nextBillDueDate.getMonth() + 1);

      // Atualizar banco com nova fatura
      await tx.bank.update({
        where: { id: bank.id },
        data: {
          billStatus: BILL_STATUS.PAID,
          currentBillAmount: 0,
          billDueDate: nextBillDueDate
        }
      });

      console.log(`   📅 Nova fatura criada - Vencimento: ${nextBillDueDate.toLocaleDateString()}`);
    });

    // Processar parcelas reservadas para nova fatura
    await this.processReservedInstallments(bank.id, currentDate);
    
    // Processar assinaturas PAID → RESERVED
    await this.processSubscriptionCycle(bank.id);
    
    // Recalcular valor da nova fatura
    const newBillAmount = await billService.calculateCurrentBillAmount(bank.id);
    
    await prisma.bank.update({
      where: { id: bank.id },
      data: {
        billStatus: BILL_STATUS.PENDING,
        currentBillAmount: newBillAmount
      }
    });

    console.log(`   💰 Nova fatura calculada: R$ ${newBillAmount}`);
  }

  /**
   * Processa parcelas RESERVED que devem ir para CURRENT_BILL
   */
  async processReservedInstallments(bankId, currentDate) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId }
      });

      if (!bank || !bank.billDueDate) return;

      // Calcular range: data de fechamento atual até +1 mês
      const currentCloseDate = bank.billDueDate;
      const nextCloseDate = new Date(currentCloseDate);
      nextCloseDate.setMonth(nextCloseDate.getMonth() + 1);

      console.log(`   🔄 Processando parcelas RESERVED (${currentCloseDate.toLocaleDateString()} - ${nextCloseDate.toLocaleDateString()})`);

      // Buscar parcelas RESERVED no range
      const reservedInstallments = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          installmentStatus: INSTALLMENT_STATUS.RESERVED,
          date: {
            gte: currentCloseDate,
            lte: nextCloseDate
          }
        }
      });

      let movedCount = 0;
      let totalMoved = 0;

      for (const installment of reservedInstallments) {
        await prisma.transaction.update({
          where: { id: installment.id },
          data: {
            installmentStatus: INSTALLMENT_STATUS.CURRENT_BILL
          }
        });

        movedCount++;
        totalMoved += installment.amount;
      }

      if (movedCount > 0) {
        console.log(`   💳 ${movedCount} parcelas movidas para CURRENT_BILL: R$ ${totalMoved}`);
      }

      return { movedCount, totalMoved };

    } catch (error) {
      console.error(`❌ Erro ao processar parcelas reservadas do banco ${bankId}:`, error);
      return { movedCount: 0, totalMoved: 0 };
    }
  }

  /**
   * Processa assinaturas RESERVED que devem ir para CURRENT_BILL
   */
  async processReservedSubscriptions(bankId, currentDate) {
    try {
      const currentDay = currentDate.getDate();

      console.log(`   🔄 Processando assinaturas RESERVED (dia atual: ${currentDay})`);

      // Buscar assinaturas RESERVED com dia de cobrança <= dia atual
      const reservedSubscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          status: SUBSCRIPTION_STATUS.PENDING, // RESERVED
          billingDay: {
            lte: currentDay
          },
          isActive: true
        }
      });

      let movedCount = 0;
      let totalMoved = 0;

      for (const subscription of reservedSubscriptions) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SUBSCRIPTION_STATUS.BILLED // CURRENT_BILL
          }
        });

        movedCount++;
        totalMoved += subscription.amount;
      }

      if (movedCount > 0) {
        console.log(`   📋 ${movedCount} assinaturas movidas para CURRENT_BILL: R$ ${totalMoved}`);
      }

      return { movedCount, totalMoved };

    } catch (error) {
      console.error(`❌ Erro ao processar assinaturas reservadas do banco ${bankId}:`, error);
      return { movedCount: 0, totalMoved: 0 };
    }
  }

  /**
   * Processa ciclo de assinaturas PAID → RESERVED
   */
  async processSubscriptionCycle(bankId) {
    try {
      const paidSubscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          status: SUBSCRIPTION_STATUS.PAID,
          isActive: true
        }
      });

      for (const subscription of paidSubscriptions) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SUBSCRIPTION_STATUS.PENDING // RESERVED
          }
        });
      }

      if (paidSubscriptions.length > 0) {
        console.log(`   🔄 ${paidSubscriptions.length} assinaturas movidas de PAID para RESERVED`);
      }

    } catch (error) {
      console.error(`❌ Erro ao processar ciclo de assinaturas do banco ${bankId}:`, error);
    }
  }

  /**
   * Calcula data de vencimento baseada no dia de vencimento
   */
  calculateBillDueDate(billDueDay, currentDate = null) {
    if (!currentDate) {
      currentDate = new Date();
    }

    const billDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), billDueDay);
    
    // Se a data de vencimento já passou este mês, usar próximo mês
    if (billDueDate <= currentDate) {
      billDueDate.setMonth(billDueDate.getMonth() + 1);
    }

    return billDueDate;
  }

  /**
   * Executa processamento manual (para testes)
   */
  async runManually() {
    console.log('🔧 Executando processamento diário manualmente...');
    return await this.processDailyBilling();
  }
}

const dailyBillingService = new DailyBillingService();
module.exports = dailyBillingService;
