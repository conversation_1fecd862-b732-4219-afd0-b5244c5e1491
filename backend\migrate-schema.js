const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateSchema() {
  try {
    console.log('🔄 Iniciando migração do schema...');

    // Verificar se a coluna isPaid já existe
    try {
      await prisma.$executeRaw`
        ALTER TABLE transactions
        ADD COLUMN isPaid BOOLEAN DEFAULT false;
      `;
      console.log('📝 Campo isPaid adicionado às transações');
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log('📝 Campo isPaid já existe nas transações');
      } else {
        throw error;
      }
    }

    // Atualizar transações existentes
    // Marcar como pagas todas as transações que não são de cartão de crédito
    await prisma.$executeRaw`
      UPDATE transactions
      SET isPaid = true
      WHERE paymentMethodId IS NULL
      OR paymentMethodId IN (
        SELECT id FROM payment_methods WHERE type != 'CREDIT'
      );
    `;

    // Verificar se a coluna cdiRate já existe
    try {
      await prisma.$executeRaw`
        ALTER TABLE savings
        ADD COLUMN cdiRate REAL DEFAULT 0;
      `;
      console.log('📝 Campo cdiRate adicionado aos cofrinhos');
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log('📝 Campo cdiRate já existe nos cofrinhos');
      } else {
        throw error;
      }
    }

    // Verificar se a coluna lastCdiUpdate já existe
    try {
      await prisma.$executeRaw`
        ALTER TABLE savings
        ADD COLUMN lastCdiUpdate DATETIME DEFAULT CURRENT_TIMESTAMP;
      `;
      console.log('📝 Campo lastCdiUpdate adicionado aos cofrinhos');
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log('📝 Campo lastCdiUpdate já existe nos cofrinhos');
      } else {
        throw error;
      }
    }

    console.log('✅ Migração concluída com sucesso!');
  } catch (error) {
    console.error('❌ Erro durante a migração:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateSchema();
