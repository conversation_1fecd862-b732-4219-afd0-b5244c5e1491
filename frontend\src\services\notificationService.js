import api from './api'

export const notificationService = {
  // Listar notificações
  async getNotifications() {
    const response = await api.get('/notifications')
    return response.data
  },

  // Marcar notificação como lida
  async markAsRead(notificationId) {
    const response = await api.patch(`/notifications/${notificationId}/read`)
    return response.data
  },

  // Marcar todas como lidas
  async markAllAsRead() {
    const response = await api.patch('/notifications/read-all')
    return response.data
  },

  // Obter contagem de não lidas
  async getUnreadCount() {
    const response = await api.get('/notifications/unreadcount')
    return response.data.count
  },

  // Criar notificação (admin)
  async createNotification(notificationData) {
    const response = await api.post('/notifications', notificationData)
    return response.data
  }
}
