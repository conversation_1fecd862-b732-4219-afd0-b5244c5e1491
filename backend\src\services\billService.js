const { PrismaClient } = require('@prisma/client');
const dateService = require('./dateService');
const { BILL_STATUS, INSTALLMENT_STATUS } = require('../constants/status');

const prisma = new PrismaClient();

class BillService {
  /**
   * Atualiza o status de todas as faturas baseado na data atual
   */
  async updateBillStatuses() {
    try {
      const currentDate = await dateService.getCurrentDate();
      console.log(`🔄 Atualizando status das faturas - Data atual: ${currentDate.toLocaleDateString()}`);

      const banks = await prisma.bank.findMany({
        where: {
          billDueDay: { not: null }
        }
      });

      for (const bank of banks) {
        await this.updateBankBillStatus(bank, currentDate);
        // ✅ REGRA 1: Processar parcelamentos reservados após atualizar status
        await this.processReservedInstallments(bank.id);
        // ✅ REGRA 0: Processar assinaturas reservadas
        const subscriptionService = require('./subscriptionService');
        await subscriptionService.processReservedSubscriptions();
      }

      console.log('✅ Status das faturas atualizados');
      return { success: true };
    } catch (error) {
      console.error('❌ Erro ao atualizar status das faturas:', error);
      throw error;
    }
  }

  /**
   * REGRA 1: Processa parcelamentos RESERVED que devem ir para CURRENT_BILL
   * Executa automaticamente quando fatura está PENDING
   */
  async processReservedInstallments(bankId) {
    try {
      console.log(`🔄 Processando parcelamentos reservados para o banco ${bankId}`);

      const bank = await prisma.bank.findFirst({
        where: { id: bankId }
      });

      if (!bank || !bank.billDueDate || bank.billStatus !== BILL_STATUS.PENDING) {
        console.log(`⏭️ Banco ${bankId} não tem fatura PENDING ou não tem data de vencimento`);
        return { success: false, reason: 'Fatura não está PENDING ou sem data de vencimento' };
      }

      // Buscar parcelamentos reservados
      const reservedInstallments = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          installmentStatus: INSTALLMENT_STATUS.RESERVED,
          date: {
            lt: bank.billDueDate // Data menor que vencimento da fatura atual
          }
        },
        include: {
          paymentMethod: true
        }
      });

      let movedCount = 0;
      let totalMoved = 0;

      for (const installment of reservedInstallments) {
        // Mover para CURRENT_BILL
        await prisma.transaction.update({
          where: { id: installment.id },
          data: {
            installmentStatus: INSTALLMENT_STATUS.CURRENT_BILL
          }
        });

        movedCount++;
        totalMoved += installment.amount;
        console.log(`💳 Parcela ${installment.id} movida de RESERVED para CURRENT_BILL: R$ ${installment.amount}`);
      }

      if (movedCount > 0) {
        // Recalcular valor da fatura
        const newBillAmount = await this.calculateCurrentBillAmount(bankId);

        await prisma.bank.update({
          where: { id: bankId },
          data: {
            currentBillAmount: newBillAmount
          }
        });

        console.log(`✅ ${movedCount} parcelas processadas - Total movido: R$ ${totalMoved}`);
        console.log(`💰 Nova fatura calculada: R$ ${newBillAmount}`);
      }

      return { success: true, movedCount, totalMoved };

    } catch (error) {
      console.error('❌ Erro ao processar parcelamentos reservados:', error);
      throw error;
    }
  }

  /**
   * Atualiza o status da fatura de um banco específico
   */
  async updateBankBillStatus(bank, currentDate = null) {
    try {
      if (!currentDate) {
        currentDate = await dateService.getCurrentDate();
      }

      if (!bank.billDueDay) {
        return { success: false, message: 'Banco não tem dia de vencimento configurado' };
      }

      // Calcular data de vencimento da fatura atual
      const billDueDate = bank.billDueDate || this.calculateBillDueDate(bank.billDueDay, currentDate);
      
      // Calcular valor atual da fatura
      const currentBillAmount = await this.calculateCurrentBillAmount(bank.id);

      let newStatus = bank.billStatus;
      let shouldGenerateNewBill = false;

      // Regra 2: Se data atual < vencimento = PENDING
      if (currentDate < billDueDate) {
        newStatus = BILL_STATUS.PENDING;
      }
      // Regra 2: Se data atual > vencimento e status != PAID e valor > 0 = OVERDUE
      else if (currentDate > billDueDate && bank.billStatus !== BILL_STATUS.PAID && currentBillAmount > 0) {
        newStatus = BILL_STATUS.OVERDUE;
      }
      // Regra 5: Se data atual >= vencimento e status != PAID e valor <= 0 = PAID + nova fatura
      else if (currentDate >= billDueDate && bank.billStatus !== BILL_STATUS.PAID && currentBillAmount <= 0) {
        newStatus = BILL_STATUS.PAID;
        shouldGenerateNewBill = true;
      }

      // Atualizar banco
      const updateData = {
        billStatus: newStatus,
        currentBillAmount
      };

      if (!bank.billDueDate) {
        updateData.billDueDate = billDueDate;
      }

      if (shouldGenerateNewBill) {
        // Gerar nova fatura para próximo mês
        const nextBillDueDate = new Date(billDueDate);
        nextBillDueDate.setMonth(nextBillDueDate.getMonth() + 1);

        updateData.billDueDate = nextBillDueDate;
        updateData.billStatus = BILL_STATUS.PENDING;
        updateData.currentBillAmount = 0;

        // REGRA 6: Processar transição de assinaturas PAID → RESERVED
        const subscriptionService = require('./subscriptionService');
        await subscriptionService.processNextBillCycle(bank.id, bank.userId);

        console.log(`📅 Nova fatura gerada para ${bank.name} - Vencimento: ${nextBillDueDate.toLocaleDateString()}`);
      }

      await prisma.bank.update({
        where: { id: bank.id },
        data: updateData
      });

      console.log(`💳 Status da fatura do ${bank.name}: ${newStatus} - Valor: R$ ${currentBillAmount}`);

      return { 
        success: true, 
        status: newStatus, 
        amount: currentBillAmount,
        newBillGenerated: shouldGenerateNewBill
      };

    } catch (error) {
      console.error(`❌ Erro ao atualizar status da fatura do banco ${bank.id}:`, error);
      throw error;
    }
  }

  /**
   * Calcula o valor atual da fatura baseado nas transações CURRENT_BILL + assinaturas CURRENT_BILL
   */
  async calculateCurrentBillAmount(bankId) {
    try {
      // Regra 6: Buscar transações de cartão de crédito com status CURRENT_BILL
      const transactions = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          installmentStatus: INSTALLMENT_STATUS.CURRENT_BILL
        }
      });

      const transactionsTotal = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);

      // REGRA 1: Buscar assinaturas com status CURRENT_BILL
      const subscriptionService = require('./subscriptionService');
      const subscriptionsTotal = await subscriptionService.calculateSubscriptionBillAmount(bankId);

      const total = transactionsTotal + subscriptionsTotal;

      console.log(`💰 Valor calculado da fatura (banco ${bankId}):`);
      console.log(`   Transações: R$ ${transactionsTotal} (${transactions.length} transações)`);
      console.log(`   Assinaturas: R$ ${subscriptionsTotal}`);
      console.log(`   Total: R$ ${total}`);

      return total;
    } catch (error) {
      console.error('❌ Erro ao calcular valor da fatura:', error);
      return 0;
    }
  }

  /**
   * Calcula a data de vencimento da fatura baseada no dia de vencimento
   */
  calculateBillDueDate(billDueDay, currentDate = null) {
    if (!currentDate) {
      currentDate = new Date();
    }

    const billDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), billDueDay);
    
    // Se a data de vencimento já passou este mês, usar próximo mês
    if (billDueDate <= currentDate) {
      billDueDate.setMonth(billDueDate.getMonth() + 1);
    }

    return billDueDate;
  }

  /**
   * Processa o pagamento de uma fatura
   */
  async payBill(bankId, paymentBankId, userId) {
    try {
      console.log(`💳 Processando pagamento da fatura - Banco: ${bankId}, Pagamento: ${paymentBankId}`);

      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      const paymentBank = await prisma.bank.findFirst({
        where: { id: paymentBankId, userId }
      });

      if (!bank || !paymentBank) {
        throw new Error('Banco não encontrado');
      }

      // Regra 4: Verificar se fatura está em atraso (bloqueia transações)
      if (bank.billStatus === BILL_STATUS.OVERDUE) {
        console.log('⚠️ Fatura em atraso - processando pagamento para desbloqueio');
      }

      const billAmount = await this.calculateCurrentBillAmount(bankId);

      if (billAmount <= 0) {
        throw new Error('Não há valor a ser pago na fatura');
      }

      // Regra 3: Verificar saldo disponível
      if (paymentBank.currentBalance < billAmount) {
        throw new Error(`Saldo insuficiente. Disponível: R$ ${paymentBank.currentBalance}, Necessário: R$ ${billAmount}`);
      }

      // Processar pagamento em transação
      await prisma.$transaction(async (tx) => {
        // Reduzir saldo do banco pagador
        await tx.bank.update({
          where: { id: paymentBankId },
          data: {
            currentBalance: {
              decrement: billAmount
            }
          }
        });

        // REGRA 6: Marcar todas as transações da fatura como pagas
        const installmentService = require('./installmentService');
        await installmentService.processBillPayment(bankId, userId);

        // REGRA 5: Marcar todas as assinaturas da fatura como pagas
        const subscriptionService = require('./subscriptionService');
        await subscriptionService.processBillPayment(bankId, userId);

        // Salvar no histórico
        await tx.billHistory.create({
          data: {
            bankId,
            amount: billAmount,
            dueDate: bank.billDueDate || new Date(),
            paidDate: await dateService.getCurrentDate(),
            paymentBankId,
            userId
          }
        });

        // Atualizar status da fatura para PAID
        await tx.bank.update({
          where: { id: bankId },
          data: {
            billStatus: BILL_STATUS.PAID,
            currentBillAmount: 0
          }
        });
      });

      console.log(`✅ Fatura paga com sucesso - Valor: R$ ${billAmount}`);

      // Processar próximas parcelas que devem ir para nova fatura
      await this.processNextInstallments(bankId, userId);

      return { 
        success: true, 
        amount: billAmount,
        message: 'Fatura paga com sucesso'
      };

    } catch (error) {
      console.error('❌ Erro ao pagar fatura:', error);
      throw error;
    }
  }

  /**
   * Processa parcelas que devem ir para a nova fatura após pagamento
   */
  async processNextInstallments(bankId, userId) {
    try {
      console.log(`🔄 Processando próximas parcelas para nova fatura - Banco: ${bankId}`);

      // Buscar parcelas reservadas que agora devem ir para fatura
      const reservedTransactions = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          installmentStatus: INSTALLMENT_STATUS.RESERVED,
          userId
        }
      });

      let movedCount = 0;
      let totalMoved = 0;

      for (const transaction of reservedTransactions) {
        // Verificar se deve ir para nova fatura atual
        const billingCycleService = require('./billingCycleService');
        const billingInfo = await billingCycleService.shouldGoToBill(
          transaction.date, 
          bankId, 
          userId
        );

        if (billingInfo.shouldGoToBill) {
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: {
              installmentStatus: INSTALLMENT_STATUS.CURRENT_BILL
            }
          });

          movedCount++;
          totalMoved += transaction.amount;
          console.log(`💳 Parcela ${transaction.id} movida para nova fatura: R$ ${transaction.amount}`);
        }
      }

      if (movedCount > 0) {
        // Recalcular valor total da nova fatura (incluindo assinaturas)
        const newBillAmount = await this.calculateCurrentBillAmount(bankId);

        await prisma.bank.update({
          where: { id: bankId },
          data: {
            currentBillAmount: newBillAmount,
            billStatus: BILL_STATUS.PENDING
          }
        });

        console.log(`✅ ${movedCount} parcelas processadas para nova fatura`);
        console.log(`💰 Nova fatura calculada: R$ ${newBillAmount} (parcelas: R$ ${totalMoved})`);
      }

      return { success: true, movedCount, totalMoved };

    } catch (error) {
      console.error('❌ Erro ao processar próximas parcelas:', error);
      throw error;
    }
  }

  /**
   * Verifica se um banco pode criar transações (não está com fatura em atraso)
   */
  async canCreateTransaction(bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank) {
        return { canCreate: false, reason: 'Banco não encontrado' };
      }

      // Regra 4: Bloquear se fatura estiver em atraso
      if (bank.billStatus === BILL_STATUS.OVERDUE) {
        return { 
          canCreate: false, 
          reason: 'Fatura em atraso. Pague a fatura para continuar criando transações.',
          billAmount: bank.currentBillAmount
        };
      }

      return { canCreate: true };

    } catch (error) {
      console.error('❌ Erro ao verificar se pode criar transação:', error);
      return { canCreate: false, reason: 'Erro interno' };
    }
  }
}

const billService = new BillService();
module.exports = billService;
