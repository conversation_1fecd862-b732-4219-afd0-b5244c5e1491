const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class BillService {
  /**
   * Método básico para calcular valor da fatura
   */
  async calculateBillAmount(bankId) {
    try {
      // Buscar transações de cartão de crédito não pagas
      const transactions = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          isPaid: false
        }
      });

      const transactionsTotal = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);

      // Buscar assinaturas ativas
      const subscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          isActive: true
        }
      });

      const subscriptionsTotal = subscriptions.reduce((sum, subscription) => sum + subscription.amount, 0);
      const total = transactionsTotal + subscriptionsTotal;

      return {
        transactionsTotal,
        subscriptionsTotal,
        total,
        transactions,
        subscriptions
      };
    } catch (error) {
      console.error('❌ Erro ao calcular valor da fatura:', error);
      return {
        transactionsTotal: 0,
        subscriptionsTotal: 0,
        total: 0,
        transactions: [],
        subscriptions: []
      };
    }
  }

  /**
   * Método básico para pagar fatura
   */
  async payBill(bankId, paymentBankId, userId) {
    try {
      const billData = await this.calculateBillAmount(bankId);

      if (billData.total <= 0) {
        throw new Error('Não há valor a ser pago na fatura');
      }

      const paymentBank = await prisma.bank.findFirst({
        where: { id: paymentBankId, userId }
      });

      if (!paymentBank || paymentBank.currentBalance < billData.total) {
        throw new Error('Saldo insuficiente para pagamento');
      }

      // Processar pagamento básico
      await prisma.$transaction(async (tx) => {
        // Reduzir saldo do banco pagador
        await tx.bank.update({
          where: { id: paymentBankId },
          data: {
            currentBalance: {
              decrement: billData.total
            }
          }
        });

        // Marcar transações como pagas
        for (const transaction of billData.transactions) {
          await tx.transaction.update({
            where: { id: transaction.id },
            data: { isPaid: true }
          });
        }
      });

      return {
        success: true,
        amount: billData.total,
        message: 'Fatura paga com sucesso'
      };

    } catch (error) {
      console.error('❌ Erro ao pagar fatura:', error);
      throw error;
    }
  }

}

const billService = new BillService();
module.exports = billService;
