const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar templates
router.get('/', async (req, res) => {
  try {
    const templates = await prisma.transactionTemplate.findMany({
      where: { userId: req.user.id },
      include: {
        category: true,
        bank: true
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(templates);
  } catch (error) {
    console.error('Erro ao buscar templates:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar template
router.post('/', async (req, res) => {
  try {
    const { name, description, type, categoryId, bankId, paymentLink, tags } = req.body;

    if (!name || !type || !categoryId || !bankId) {
      return res.status(400).json({ error: 'Nome, tipo, categoria e banco são obrigatórios' });
    }

    const template = await prisma.transactionTemplate.create({
      data: {
        name,
        description,
        type,
        categoryId,
        bankId,
        paymentLink,
        tags,
        userId: req.user.id
      },
      include: {
        category: true,
        bank: true
      }
    });

    res.status(201).json(template);
  } catch (error) {
    console.error('Erro ao criar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar template
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, type, categoryId, bankId, paymentLink, tags, isActive } = req.body;

    const template = await prisma.transactionTemplate.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template não encontrado' });
    }

    const updatedTemplate = await prisma.transactionTemplate.update({
      where: { id },
      data: {
        name: name || template.name,
        description: description !== undefined ? description : template.description,
        type: type || template.type,
        categoryId: categoryId || template.categoryId,
        bankId: bankId || template.bankId,
        paymentLink: paymentLink !== undefined ? paymentLink : template.paymentLink,
        tags: tags !== undefined ? tags : template.tags,
        isActive: isActive !== undefined ? isActive : template.isActive
      },
      include: {
        category: true,
        bank: true
      }
    });

    res.json(updatedTemplate);
  } catch (error) {
    console.error('Erro ao atualizar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar template
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const template = await prisma.transactionTemplate.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template não encontrado' });
    }

    await prisma.transactionTemplate.delete({
      where: { id }
    });

    res.json({ message: 'Template deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Usar template para criar transação
router.post('/:id/use', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, description, date, paymentMethodId } = req.body;

    if (!amount) {
      return res.status(400).json({ error: 'Valor é obrigatório' });
    }

    const template = await prisma.transactionTemplate.findFirst({
      where: { id, userId: req.user.id },
      include: {
        category: true,
        bank: true
      }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template não encontrado' });
    }

    if (!template.isActive) {
      return res.status(400).json({ error: 'Template está inativo' });
    }

    // Criar transação baseada no template
    const transaction = await prisma.transaction.create({
      data: {
        description: description || template.name,
        amount: Math.abs(amount),
        type: template.type,
        date: date ? new Date(date) : new Date(),
        categoryId: template.categoryId,
        bankId: template.bankId,
        paymentMethodId: paymentMethodId || null,
        userId: req.user.id
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true
      }
    });

    // Atualizar saldo do banco
    const balanceChange = template.type === 'INCOME' ? amount : -amount;
    await prisma.bank.update({
      where: { id: template.bankId },
      data: {
        currentBalance: {
          increment: balanceChange
        }
      }
    });

    res.status(201).json({
      message: 'Transação criada com sucesso usando template',
      transaction
    });
  } catch (error) {
    console.error('Erro ao usar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter templates ativos para seleção rápida
router.get('/active', async (req, res) => {
  try {
    const templates = await prisma.transactionTemplate.findMany({
      where: { 
        userId: req.user.id,
        isActive: true
      },
      include: {
        category: true,
        bank: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(templates);
  } catch (error) {
    console.error('Erro ao buscar templates ativos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
