const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const upload = require('../middleware/upload');
const cloudinary = require('../config/cloudinary');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar templates
router.get('/', async (req, res) => {
  try {
    const templates = await prisma.transactionTemplate.findMany({
      where: { userId: req.user.id },
      include: {
        category: true,
        bank: true,
        transactionContact: true
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(templates);
  } catch (error) {
    console.error('Erro ao buscar templates:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar template
router.post('/', async (req, res) => {
  try {
    const { name, description, type, categoryId, bankId, transactionContactId, tags } = req.body;

    if (!name || !type || !categoryId || !bankId) {
      return res.status(400).json({ error: 'Nome, tipo, categoria e banco são obrigatórios' });
    }

    // Converter tags array para string se necessário
    let tagsString = null;
    if (tags && Array.isArray(tags) && tags.length > 0) {
      tagsString = tags.join(',');
    } else if (typeof tags === 'string' && tags.trim()) {
      tagsString = tags;
    }

    const template = await prisma.transactionTemplate.create({
      data: {
        name,
        description,
        type,
        categoryId,
        bankId,
        transactionContactId: transactionContactId || null,
        tags: tagsString,
        userId: req.user.id
      },
      include: {
        category: true,
        bank: true,
        transactionContact: true
      }
    });

    res.status(201).json(template);
  } catch (error) {
    console.error('Erro ao criar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar template
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, type, categoryId, bankId, transactionContactId, tags, isActive } = req.body;

    const template = await prisma.transactionTemplate.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template não encontrado' });
    }

    // Converter tags array para string se necessário
    let tagsString = template.tags;
    if (tags !== undefined) {
      if (Array.isArray(tags) && tags.length > 0) {
        tagsString = tags.join(',');
      } else if (typeof tags === 'string' && tags.trim()) {
        tagsString = tags;
      } else {
        tagsString = null;
      }
    }

    const updatedTemplate = await prisma.transactionTemplate.update({
      where: { id },
      data: {
        name: name || template.name,
        description: description !== undefined ? description : template.description,
        type: type || template.type,
        categoryId: categoryId || template.categoryId,
        bankId: bankId || template.bankId,
        transactionContactId: transactionContactId !== undefined ? transactionContactId : template.transactionContactId,
        tags: tagsString,
        isActive: isActive !== undefined ? isActive : template.isActive
      },
      include: {
        category: true,
        bank: true,
        transactionContact: true
      }
    });

    res.json(updatedTemplate);
  } catch (error) {
    console.error('Erro ao atualizar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar template
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const template = await prisma.transactionTemplate.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template não encontrado' });
    }

    await prisma.transactionTemplate.delete({
      where: { id }
    });

    res.json({ message: 'Template deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Usar template para criar transação
router.post('/:id/use', upload.single('receipt'), async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, description, date, time, paymentMethodId } = req.body;

    if (!amount) {
      return res.status(400).json({ error: 'Valor é obrigatório' });
    }

    const template = await prisma.transactionTemplate.findFirst({
      where: { id, userId: req.user.id },
      include: {
        category: true,
        bank: true,
        transactionContact: true
      }
    });

    if (!template) {
      return res.status(404).json({ error: 'Template não encontrado' });
    }

    if (!template.isActive) {
      return res.status(400).json({ error: 'Template está inativo' });
    }

    // Processar upload do comprovante se fornecido
    let receiptUrl = null;
    if (req.file) {
      try {
        const result = await new Promise((resolve, reject) => {
          cloudinary.uploader.upload_stream(
            {
              resource_type: 'auto',
              folder: 'template_receipts'
            },
            (error, result) => {
              if (error) reject(error);
              else resolve(result);
            }
          ).end(req.file.buffer);
        });
        receiptUrl = result.secure_url;
        console.log('✅ Comprovante do template enviado:', receiptUrl);
      } catch (uploadError) {
        console.error('❌ Erro no upload do comprovante:', uploadError);
      }
    }

    // Usar inteligência de data/hora
    let transactionDate;
    if (date && time) {
      transactionDate = new Date(`${date}T${time}:00`);
    } else if (date) {
      const now = new Date();
      const selectedDate = new Date(date);
      const today = new Date().toDateString();

      if (selectedDate.toDateString() === today) {
        transactionDate = now; // Horário atual se for hoje
      } else {
        transactionDate = new Date(`${date}T00:00:00`); // Início do dia se for outra data
      }
    } else {
      transactionDate = new Date(); // Data/hora atual
    }

    // Criar transação baseada no template
    const transaction = await prisma.transaction.create({
      data: {
        description: description || template.name,
        amount: Math.abs(amount),
        type: template.type,
        date: transactionDate,
        categoryId: template.categoryId,
        bankId: template.bankId,
        paymentMethodId: paymentMethodId || null,
        transactionContactId: template.transactionContactId,
        receiptUrl: receiptUrl,
        isFromTemplate: true, // Flag para indicar que veio de template
        userId: req.user.id
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true
      }
    });

    // Atualizar saldo do banco
    const balanceChange = template.type === 'INCOME' ? amount : -amount;
    await prisma.bank.update({
      where: { id: template.bankId },
      data: {
        currentBalance: {
          increment: balanceChange
        }
      }
    });

    res.status(201).json({
      message: 'Transação criada com sucesso usando template',
      transaction
    });
  } catch (error) {
    console.error('Erro ao usar template:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter templates ativos para seleção rápida
router.get('/active', async (req, res) => {
  try {
    const templates = await prisma.transactionTemplate.findMany({
      where: { 
        userId: req.user.id,
        isActive: true
      },
      include: {
        category: true,
        bank: true,
        transactionContact: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(templates);
  } catch (error) {
    console.error('Erro ao buscar templates ativos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
