import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Copy, ExternalLink, Tag as TagIcon } from 'lucide-react'
import { transactionTemplateService } from '../services/transactionTemplateService'
import { categoryService } from '../services/categoryService'
import { bankService } from '../services/bankService'
import toast from 'react-hot-toast'

function TemplateManager() {
  const [templates, setTemplates] = useState([])
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState(null)
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    type: 'EXPENSE',
    categoryId: '',
    bankId: '',
    paymentLink: '',
    tags: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      console.log('🔄 Carregando dados para TemplateManager...')
      const [templatesData, categoriesData, banksData] = await Promise.all([
        transactionTemplateService.getTemplates(),
        categoryService.getCategories(),
        bankService.getBanks()
      ])
      console.log('📊 Dados carregados:', {
        templates: templatesData?.length || 0,
        categories: categoriesData?.length || 0,
        banks: banksData?.length || 0
      })
      setTemplates(templatesData)
      setCategories(categoriesData)
      setBanks(banksData)
    } catch (error) {
      console.error('❌ Erro ao buscar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      if (editingTemplate) {
        await transactionTemplateService.updateTemplate(editingTemplate.id, newTemplate)
        toast.success('Template atualizado com sucesso!')
      } else {
        await transactionTemplateService.createTemplate(newTemplate)
        toast.success('Template criado com sucesso!')
      }
      setShowModal(false)
      setEditingTemplate(null)
      setNewTemplate({
        name: '',
        description: '',
        type: 'EXPENSE',
        categoryId: '',
        bankId: '',
        paymentLink: '',
        tags: ''
      })
      fetchData()
    } catch (error) {
      toast.error('Erro ao salvar template')
    }
  }

  const handleEdit = (template) => {
    setEditingTemplate(template)
    setNewTemplate({
      name: template.name,
      description: template.description || '',
      type: template.type,
      categoryId: template.categoryId,
      bankId: template.bankId,
      paymentLink: template.paymentLink || '',
      tags: template.tags || ''
    })
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja excluir este template?')) {
      try {
        await transactionTemplateService.deleteTemplate(id)
        toast.success('Template excluído com sucesso!')
        fetchData()
      } catch (error) {
        toast.error('Erro ao excluir template')
      }
    }
  }

  const handleToggleActive = async (template) => {
    try {
      await transactionTemplateService.updateTemplate(template.id, {
        isActive: !template.isActive
      })
      toast.success(`Template ${template.isActive ? 'desativado' : 'ativado'} com sucesso!`)
      fetchData()
    } catch (error) {
      toast.error('Erro ao alterar status do template')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Templates de Transações</h2>
          <p className="text-gray-600 mt-1">
            Crie templates para transações recorrentes e acelere seu cadastro
          </p>
        </div>
        <button
          onClick={() => {
            setEditingTemplate(null)
            setNewTemplate({
              name: '',
              description: '',
              type: 'EXPENSE',
              categoryId: '',
              bankId: '',
              paymentLink: '',
              tags: ''
            })
            setShowModal(true)
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Novo Template
        </button>
      </div>

      {/* Lista de Templates */}
      {templates.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
          <Copy className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum template encontrado
          </h3>
          <p className="text-gray-600 mb-6">
            Crie seu primeiro template para acelerar o cadastro de transações
          </p>
          <button
            onClick={() => setShowModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Criar Template
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`bg-white rounded-xl border shadow-sm hover:shadow-md transition-all duration-200 ${
                !template.isActive ? 'opacity-60' : ''
              }`}
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      {template.name}
                    </h3>
                    {template.description && (
                      <p className="text-sm text-gray-600 mb-2">
                        {template.description}
                      </p>
                    )}
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    template.type === 'INCOME' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {template.type === 'INCOME' ? 'Receita' : 'Despesa'}
                  </span>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="font-medium">Categoria:</span>
                    <span className="ml-2">{template.category?.name}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="font-medium">Banco:</span>
                    <span className="ml-2">{template.bank?.name}</span>
                  </div>
                  {template.paymentLink && (
                    <div className="flex items-center text-sm text-gray-600">
                      <span className="font-medium">Link:</span>
                      <a 
                        href={template.paymentLink} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="ml-2 text-blue-500 hover:text-blue-700 flex items-center gap-1"
                      >
                        <ExternalLink className="h-3 w-3" />
                        Pagamento
                      </a>
                    </div>
                  )}
                  {template.tags && (
                    <div className="flex items-center text-sm text-gray-600">
                      <TagIcon className="h-3 w-3 mr-1" />
                      <span>{template.tags}</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleEdit(template)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Editar"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(template.id)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                      title="Excluir"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  <button
                    onClick={() => handleToggleActive(template)}
                    className={`px-3 py-1 text-xs rounded-full transition-colors ${
                      template.isActive
                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    {template.isActive ? 'Ativo' : 'Inativo'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal de Criação/Edição */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {editingTemplate ? 'Editar Template' : 'Novo Template'}
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Nome */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome do Template *
                </label>
                <input
                  type="text"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Pizza da Sexta"
                  required
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={newTemplate.description}
                  onChange={(e) => setNewTemplate({ ...newTemplate, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição opcional"
                  rows="2"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Tipo */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo *
                  </label>
                  <select
                    value={newTemplate.type}
                    onChange={(e) => setNewTemplate({ ...newTemplate, type: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="EXPENSE">Despesa</option>
                    <option value="INCOME">Receita</option>
                  </select>
                </div>

                {/* Categoria */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categoria *
                  </label>
                  <select
                    value={newTemplate.categoryId}
                    onChange={(e) => setNewTemplate({ ...newTemplate, categoryId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Selecione uma categoria</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.icon} {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Banco */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Banco *
                </label>
                <select
                  value={newTemplate.bankId}
                  onChange={(e) => setNewTemplate({ ...newTemplate, bankId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Link de Pagamento */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Link de Pagamento
                </label>
                <input
                  type="url"
                  value={newTemplate.paymentLink}
                  onChange={(e) => setNewTemplate({ ...newTemplate, paymentLink: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="PIX, link da empresa, etc."
                />
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <input
                  type="text"
                  value={newTemplate.tags}
                  onChange={(e) => setNewTemplate({ ...newTemplate, tags: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Separadas por vírgula"
                />
              </div>

              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingTemplate ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default TemplateManager
