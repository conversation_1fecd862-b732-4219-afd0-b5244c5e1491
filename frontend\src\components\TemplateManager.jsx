import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Copy, ExternalLink, Tag as TagIcon, Play, Pause } from 'lucide-react'
import { transactionTemplateService } from '../services/transactionTemplateService'
import { categoryService } from '../services/categoryService'
import { bankService } from '../services/bankService'
import CurrencyInput from './CurrencyInput'
import toast from 'react-hot-toast'

function TemplateManager() {
  const [templates, setTemplates] = useState([])
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState(null)
  const [showUseModal, setShowUseModal] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [transactionData, setTransactionData] = useState({
    amount: 0,
    description: '',
    date: new Date().toISOString().split('T')[0]
  })
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    type: 'EXPENSE',
    categoryId: '',
    bankId: '',
    paymentLink: '',
    tags: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      console.log('🔄 Carregando dados para TemplateManager...')
      const [templatesData, categoriesData, banksData] = await Promise.all([
        transactionTemplateService.getTemplates(),
        categoryService.getCategories(),
        bankService.getBanks()
      ])
      console.log('📊 Dados carregados:', {
        templates: templatesData?.length || 0,
        categories: categoriesData?.length || 0,
        banks: banksData?.length || 0
      })
      setTemplates(templatesData)
      setCategories(categoriesData)
      setBanks(banksData)
    } catch (error) {
      console.error('❌ Erro ao buscar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      if (editingTemplate) {
        await transactionTemplateService.updateTemplate(editingTemplate.id, newTemplate)
        toast.success('Template atualizado com sucesso!')
      } else {
        await transactionTemplateService.createTemplate(newTemplate)
        toast.success('Template criado com sucesso!')
      }
      setShowModal(false)
      setEditingTemplate(null)
      setNewTemplate({
        name: '',
        description: '',
        type: 'EXPENSE',
        categoryId: '',
        bankId: '',
        paymentLink: '',
        tags: ''
      })
      fetchData()
    } catch (error) {
      toast.error('Erro ao salvar template')
    }
  }

  const handleEdit = (template) => {
    setEditingTemplate(template)
    setNewTemplate({
      name: template.name,
      description: template.description || '',
      type: template.type,
      categoryId: template.categoryId,
      bankId: template.bankId,
      paymentLink: template.paymentLink || '',
      tags: template.tags || ''
    })
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja excluir este template?')) {
      try {
        await transactionTemplateService.deleteTemplate(id)
        toast.success('Template excluído com sucesso!')
        fetchData()
      } catch (error) {
        toast.error('Erro ao excluir template')
      }
    }
  }

  const handleUseTemplate = (template) => {
    setSelectedTemplate(template)
    setTransactionData({
      amount: 0,
      description: template.name,
      date: new Date().toISOString().split('T')[0]
    })
    setShowUseModal(true)
  }

  const handleCreateTransaction = async () => {
    try {
      if (!transactionData.amount || transactionData.amount <= 0) {
        toast.error('Valor é obrigatório e deve ser maior que zero')
        return
      }

      await transactionTemplateService.useTemplate(selectedTemplate.id, transactionData)
      toast.success('Transação criada com sucesso!')
      setShowUseModal(false)
      setSelectedTemplate(null)
      setTransactionData({
        amount: 0,
        description: '',
        date: new Date().toISOString().split('T')[0]
      })
    } catch (error) {
      toast.error('Erro ao criar transação')
    }
  }

  const handleToggleActive = async (template) => {
    try {
      await transactionTemplateService.updateTemplate(template.id, {
        isActive: !template.isActive
      })
      toast.success(`Template ${template.isActive ? 'desativado' : 'ativado'} com sucesso!`)
      fetchData()
    } catch (error) {
      toast.error('Erro ao alterar status do template')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Templates de Transações</h2>
          <p className="text-gray-600 mt-1">
            Crie templates para transações recorrentes e acelere seu cadastro
          </p>
        </div>
        <button
          onClick={() => {
            setEditingTemplate(null)
            setNewTemplate({
              name: '',
              description: '',
              type: 'EXPENSE',
              categoryId: '',
              bankId: '',
              paymentLink: '',
              tags: ''
            })
            setShowModal(true)
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Novo Template
        </button>
      </div>

      {/* Lista de Templates */}
      {templates.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
          <Copy className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum template encontrado
          </h3>
          <p className="text-gray-600 mb-6">
            Crie seu primeiro template para acelerar o cadastro de transações
          </p>
          <button
            onClick={() => setShowModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Criar Template
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`bg-gradient-to-br from-white to-gray-50 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] ${
                template.isActive
                  ? template.type === 'INCOME'
                    ? 'border-green-200 hover:border-green-300'
                    : 'border-blue-200 hover:border-blue-300'
                  : 'border-gray-200 hover:border-gray-300 opacity-70'
              }`}
            >
              {/* Header do Card */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`w-3 h-3 rounded-full ${
                        template.isActive
                          ? template.type === 'INCOME' ? 'bg-green-500' : 'bg-blue-500'
                          : 'bg-gray-400'
                      }`}></div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {template.name}
                      </h3>
                    </div>
                    <div className="flex items-center gap-2 mb-3">
                      <span className={`px-3 py-1 text-xs font-semibold rounded-full border ${
                        template.type === 'INCOME'
                          ? 'bg-green-100 text-green-700 border-green-200'
                          : 'bg-blue-100 text-blue-700 border-blue-200'
                      }`}>
                        {template.type === 'INCOME' ? '💰 Receita' : '💸 Despesa'}
                      </span>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full border ${
                        template.isActive
                          ? 'bg-green-100 text-green-700 border-green-200'
                          : 'bg-gray-100 text-gray-600 border-gray-200'
                      }`}>
                        {template.isActive ? '🟢 Ativo' : '⚫ Inativo'}
                      </span>
                    </div>
                    {template.description && (
                      <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                        {template.description}
                      </p>
                    )}
                  </div>
                </div>

                {/* Detalhes do Template */}
                <div className="bg-white rounded-xl p-4 border border-gray-100 mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    📋 Detalhes
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-gray-600">Categoria:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{template.category?.icon}</span>
                        <span className="text-sm font-medium text-gray-900">{template.category?.name}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-gray-600">Banco:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{template.bank?.icon}</span>
                        <span className="text-sm font-medium text-gray-900">{template.bank?.name}</span>
                      </div>
                    </div>
                    {template.paymentLink && (
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-600">Link:</span>
                        <a
                          href={template.paymentLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:text-blue-700 flex items-center gap-1 text-sm font-medium"
                        >
                          <ExternalLink className="h-3 w-3" />
                          Acessar
                        </a>
                      </div>
                    )}
                    {template.tags && (
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-600">Tags:</span>
                        <div className="flex items-center gap-1">
                          <TagIcon className="h-3 w-3 text-gray-500" />
                          <span className="text-sm font-medium text-gray-900">{template.tags}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Ações */}
              <div className="px-6 pb-6">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleUseTemplate(template)}
                    disabled={!template.isActive}
                    className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-xl font-semibold transition-all duration-200 ${
                      template.isActive
                        ? template.type === 'INCOME'
                          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl'
                          : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                    title="Usar template para criar transação"
                  >
                    <Copy className="h-4 w-4" />
                    Usar Template
                  </button>

                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleToggleActive(template)}
                      className={`p-3 rounded-xl transition-all duration-200 ${
                        template.isActive
                          ? 'bg-orange-100 text-orange-600 hover:bg-orange-200'
                          : 'bg-green-100 text-green-600 hover:bg-green-200'
                      }`}
                      title={template.isActive ? 'Desativar template' : 'Ativar template'}
                    >
                      {template.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </button>

                    <button
                      onClick={() => handleEdit(template)}
                      className="p-3 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded-xl transition-all duration-200"
                      title="Editar template"
                    >
                      <Edit className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => handleDelete(template.id)}
                      className="p-3 bg-red-100 text-red-600 hover:bg-red-200 rounded-xl transition-all duration-200"
                      title="Excluir template"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal de Criação/Edição */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {editingTemplate ? 'Editar Template' : 'Novo Template'}
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Nome */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome do Template *
                </label>
                <input
                  type="text"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Pizza da Sexta"
                  required
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={newTemplate.description}
                  onChange={(e) => setNewTemplate({ ...newTemplate, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição opcional"
                  rows="2"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Tipo */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo *
                  </label>
                  <select
                    value={newTemplate.type}
                    onChange={(e) => setNewTemplate({ ...newTemplate, type: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="EXPENSE">Despesa</option>
                    <option value="INCOME">Receita</option>
                  </select>
                </div>

                {/* Categoria */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categoria *
                  </label>
                  <select
                    value={newTemplate.categoryId}
                    onChange={(e) => setNewTemplate({ ...newTemplate, categoryId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Selecione uma categoria</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.icon} {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Banco */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Banco *
                </label>
                <select
                  value={newTemplate.bankId}
                  onChange={(e) => setNewTemplate({ ...newTemplate, bankId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Link de Pagamento */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Link de Pagamento
                </label>
                <input
                  type="url"
                  value={newTemplate.paymentLink}
                  onChange={(e) => setNewTemplate({ ...newTemplate, paymentLink: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="PIX, link da empresa, etc."
                />
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <input
                  type="text"
                  value={newTemplate.tags}
                  onChange={(e) => setNewTemplate({ ...newTemplate, tags: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Separadas por vírgula"
                />
              </div>

              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingTemplate ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal de Usar Template */}
      {showUseModal && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                Usar Template: {selectedTemplate.name}
              </h3>
              <p className="text-gray-600 mt-1">
                Preencha os dados para criar a transação
              </p>
            </div>

            <div className="p-6 space-y-4">
              {/* Informações do Template */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    selectedTemplate.type === 'INCOME'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {selectedTemplate.type === 'INCOME' ? '💰 Receita' : '💸 Despesa'}
                  </span>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>📂 {selectedTemplate.category?.name}</div>
                  <div>🏦 {selectedTemplate.bank?.name}</div>
                </div>
              </div>

              {/* Valor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valor *
                </label>
                <CurrencyInput
                  value={transactionData.amount}
                  onChange={(value) => setTransactionData({ ...transactionData, amount: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <input
                  type="text"
                  value={transactionData.description}
                  onChange={(e) => setTransactionData({ ...transactionData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição da transação"
                />
              </div>

              {/* Data */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Data
                </label>
                <input
                  type="date"
                  value={transactionData.date}
                  onChange={(e) => setTransactionData({ ...transactionData, date: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Link de Pagamento */}
              {selectedTemplate.paymentLink && (
                <div className="bg-blue-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-blue-700">Link de Pagamento:</span>
                    <a
                      href={selectedTemplate.paymentLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 flex items-center gap-1 text-sm font-medium"
                    >
                      <ExternalLink className="h-3 w-3" />
                      Acessar
                    </a>
                  </div>
                </div>
              )}
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowUseModal(false)
                  setSelectedTemplate(null)
                  setTransactionData({
                    amount: 0,
                    description: '',
                    date: new Date().toISOString().split('T')[0]
                  })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateTransaction}
                className={`px-6 py-2 text-white rounded-lg transition-colors ${
                  selectedTemplate.type === 'INCOME'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                Criar Transação
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TemplateManager
