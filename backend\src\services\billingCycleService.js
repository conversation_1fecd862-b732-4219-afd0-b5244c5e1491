const { PrismaClient } = require('@prisma/client');
const dateService = require('./dateService');
const { INSTALLMENT_STATUS, SUBSCRIPTION_STATUS } = require('../constants/status');
const prisma = new PrismaClient();

class BillingCycleService {
  
  /**
   * Determina se uma transação deve ir para a fatura atual ou próxima fatura
   * baseado no período de faturamento real do cartão de crédito
   */
  async shouldGoToBill(transactionDate, bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank || !bank.billDueDay) {
        // Se não tem dia de fechamento definido, sempre vai para fatura
        return { shouldGoToBill: true, shouldReduceBankBalance: false };
      }

      const transactionDateObj = new Date(transactionDate);
      const today = await dateService.getCurrentDate();

      // ✅ CALCULAR PERÍODO DA FATURA ATUAL (LÓGICA REAL DO CARTÃO)
      // Encontrar o último fechamento que já passou
      let currentBillStart = new Date(today.getFullYear(), today.getMonth(), bank.billDueDay);

      // Se ainda não chegou no fechamento deste mês, a fatura atual começou no mês passado
      if (today.getDate() < bank.billDueDay) {
        currentBillStart.setMonth(currentBillStart.getMonth() - 1);
      }

      // Próximo fechamento (fim da fatura atual)
      const currentBillEnd = new Date(currentBillStart);
      currentBillEnd.setMonth(currentBillEnd.getMonth() + 1);

      // ✅ LÓGICA CORRETA COMPLETA:
      // 1. No período atual → VAI PARA FATURA ATUAL
      // 2. Antes do período → REDUZ SALDO (fatura já foi paga)
      // 3. Após o período → FICA RESERVADA (vai para próxima fatura)

      const isInCurrentBillingPeriod = transactionDateObj >= currentBillStart && transactionDateObj < currentBillEnd;
      const isBeforeCurrentPeriod = transactionDateObj < currentBillStart;
      const isAfterCurrentPeriod = transactionDateObj >= currentBillEnd;

      let shouldGoToBill = false;
      let shouldReduceBankBalance = false;
      let shouldStayReserved = false;
      let status = '';

      if (isInCurrentBillingPeriod) {
        shouldGoToBill = true;
        status = 'VAI PARA FATURA ATUAL';
      } else if (isBeforeCurrentPeriod) {
        shouldReduceBankBalance = true;
        status = 'REDUZ SALDO DO BANCO (fatura já paga)';
      } else if (isAfterCurrentPeriod) {
        shouldStayReserved = true;
        status = 'FICA RESERVADA (próxima fatura)';
      }

      console.log(`📅 Análise de faturamento (LÓGICA REAL COMPLETA):`);
      console.log(`   Data da transação: ${transactionDateObj.toLocaleDateString()}`);
      console.log(`   Hoje: ${today.toLocaleDateString()}`);
      console.log(`   Período fatura atual: ${currentBillStart.toLocaleDateString()} até ${currentBillEnd.toLocaleDateString()}`);
      console.log(`   No período atual: ${isInCurrentBillingPeriod}`);
      console.log(`   Antes do período: ${isBeforeCurrentPeriod}`);
      console.log(`   Após o período: ${isAfterCurrentPeriod}`);
      console.log(`   RESULTADO: ${status}`);

      return {
        shouldGoToBill,
        shouldReduceBankBalance,
        shouldStayReserved,
        currentBillStart,
        currentBillEnd,
        bank
      };

    } catch (error) {
      console.error('❌ Erro ao verificar ciclo de faturamento:', error);
      // Em caso de erro, usar comportamento padrão (vai para fatura)
      return { shouldGoToBill: true, shouldReduceBankBalance: false };
    }
  }

  /**
   * Verifica se uma fatura pode ser atualizada (só se estiver paga)
   */
  async canUpdateBill(bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId },
        include: {
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      if (!bank || !bank.billDueDay) {
        return { canUpdate: true, reason: 'Banco sem ciclo de faturamento' };
      }

      // Verificar se todas as faturas dos cartões estão pagas
      for (const paymentMethod of bank.paymentMethods) {
        if (!paymentMethod.isBillPaid && paymentMethod.currentBill > 0) {
          return {
            canUpdate: false,
            reason: `Fatura do cartão ${paymentMethod.name} não foi paga (R$ ${paymentMethod.currentBill})`,
            unpaidAmount: paymentMethod.currentBill,
            paymentMethodName: paymentMethod.name
          };
        }
      }

      return { canUpdate: true, reason: 'Todas as faturas estão pagas' };

    } catch (error) {
      console.error('❌ Erro ao verificar se pode atualizar fatura:', error);
      return { canUpdate: false, reason: 'Erro interno' };
    }
  }

  /**
   * Processa o fechamento de faturas baseado nas datas de vencimento
   * APENAS SE AS FATURAS ESTIVEREM PAGAS
   */
  async processBillingCycles() {
    try {
      console.log('🔄 Processando ciclos de faturamento...');

      const today = await dateService.getCurrentDate();

      // Buscar bancos com dia de fechamento definido
      const banksWithBilling = await prisma.bank.findMany({
        where: {
          billDueDay: { not: null }
        },
        include: {
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      for (const bank of banksWithBilling) {
        // Verificar se hoje é dia de fechamento
        if (today.getDate() === bank.billDueDay) {
          console.log(`📅 Verificando fechamento para banco ${bank.name} (dia ${bank.billDueDay})`);

          // ✅ VERIFICAR SE PODE ATUALIZAR FATURA (SÓ SE ESTIVER PAGA)
          const canUpdate = await this.canUpdateBill(bank.id, bank.userId);

          if (!canUpdate.canUpdate) {
            console.log(`❌ Não é possível processar fechamento: ${canUpdate.reason}`);
            continue; // Pula este banco
          }

          console.log(`✅ Processando fechamento para banco ${bank.name} - faturas pagas`);

          for (const paymentMethod of bank.paymentMethods) {
            await this.closeBillingCycle(paymentMethod.id, bank.id);
          }
        }
      }

      console.log('✅ Processamento de ciclos de faturamento concluído');
      return { success: true };

    } catch (error) {
      console.error('❌ Erro ao processar ciclos de faturamento:', error);
      throw error;
    }
  }

  /**
   * Processa assinaturas que devem ser cobradas hoje
   */
  async processDailySubscriptions() {
    try {
      console.log('🔄 Processando assinaturas diárias...');

      const today = await dateService.getCurrentDate();
      const currentDay = today.getDate();
      const currentMonth = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0');

      // Buscar assinaturas que devem ser cobradas hoje
      const subscriptionsToProcess = await prisma.subscription.findMany({
        where: {
          isActive: true,
          billingDay: currentDay,
          OR: [
            { lastBilledMonth: null },
            { lastBilledMonth: { not: currentMonth } }
          ]
        },
        include: {
          paymentMethod: {
            include: {
              bank: true
            }
          },
          category: true
        }
      });

      let processedCount = 0;

      for (const subscription of subscriptionsToProcess) {
        try {
          // ✅ VERIFICAR SE DEVE IR PARA FATURA ATUAL OU PRÓXIMA
          const billingInfo = await this.shouldGoToBill(
            new Date(),
            subscription.paymentMethod.bank.id,
            subscription.userId
          );

          if (billingInfo.shouldGoToBill) {
            // Adicionar à fatura atual
            await prisma.paymentMethod.update({
              where: { id: subscription.paymentMethodId },
              data: {
                currentBill: {
                  increment: subscription.amount
                },
                isBillPaid: false
              }
            });

            console.log(`💳 Assinatura ${subscription.name} adicionada à fatura atual: R$ ${subscription.amount}`);
          } else {
            // Criar transação reservada para próxima fatura
            await prisma.transaction.create({
              data: {
                description: `Assinatura - ${subscription.name}`,
                amount: subscription.amount,
                type: 'EXPENSE',
                date: new Date(),
                paymentMethodId: subscription.paymentMethodId,
                categoryId: subscription.categoryId,
                userId: subscription.userId,
                isPaid: true // Reservada para próxima fatura
              }
            });

            console.log(`⏳ Assinatura ${subscription.name} reservada para próxima fatura: R$ ${subscription.amount}`);
          }

          // Marcar como cobrado este mês
          await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
              lastBilledMonth: currentMonth,
              isPaidThisMonth: false
            }
          });

          processedCount++;
        } catch (error) {
          console.error(`❌ Erro ao processar assinatura ${subscription.id}:`, error);
        }
      }

      console.log(`✅ ${processedCount} assinaturas processadas automaticamente`);
      return {
        success: true,
        processedCount,
        subscriptions: subscriptionsToProcess.map(s => ({
          id: s.id,
          name: s.name,
          amount: s.amount,
          billingDay: s.billingDay
        }))
      };

    } catch (error) {
      console.error('❌ Erro ao processar assinaturas diárias:', error);
      throw error;
    }
  }

  /**
   * Fecha o ciclo de faturamento de um cartão específico
   */
  async closeBillingCycle(paymentMethodId, bankId) {
    try {
      console.log(`🔄 Fechando ciclo de faturamento para cartão ${paymentMethodId}`);

      // Buscar transações não pagas que devem ir para a próxima fatura
      const pendingTransactions = await prisma.transaction.findMany({
        where: {
          paymentMethodId,
          isPaid: true, // Transações que estavam "aguardando"
          date: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) // Deste mês
          }
        }
      });

      let totalToAddToBill = 0;

      for (const transaction of pendingTransactions) {
        // Verificar se deve ir para fatura agora
        const billingInfo = await this.shouldGoToBill(transaction.date, bankId, transaction.userId);

        if (billingInfo.shouldGoToBill) {
          // Marcar como não paga (vai para fatura)
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: { isPaid: false }
          });

          totalToAddToBill += transaction.amount;
          console.log(`💳 Transação ${transaction.id} movida para fatura: R$ ${transaction.amount}`);
        }
      }

      // Atualizar fatura do cartão
      if (totalToAddToBill > 0) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: {
              increment: totalToAddToBill
            },
            isBillPaid: false
          }
        });

        console.log(`💳 Fatura atualizada: +R$ ${totalToAddToBill}`);
      }

      return { success: true, amountAdded: totalToAddToBill };

    } catch (error) {
      console.error('❌ Erro ao fechar ciclo de faturamento:', error);
      throw error;
    }
  }

  /**
   * Processa parcelas reservadas que agora devem ir para a nova fatura
   * Chamado após pagamento de fatura
   */
  async processReservedInstallments(paymentMethodId, bankId, userId) {
    try {
      console.log(`🔄 Processando parcelas reservadas para cartão ${paymentMethodId}`);

      // ✅ BUSCAR TODAS AS PARCELAS QUE ESTÃO AGUARDANDO PROCESSAMENTO
      // Incluir parcelas que estão marcadas como "pagas" mas são de parcelamentos futuros
      const allInstallments = await prisma.transaction.findMany({
        where: {
          paymentMethodId,
          userId,
          parentTransactionId: { not: null } // Apenas parcelas
        },
        orderBy: { date: 'asc' }
      });

      let totalToAddToBill = 0;
      let processedCount = 0;

      console.log(`📊 Encontradas ${allInstallments.length} parcelas para análise`);

      for (const transaction of allInstallments) {
        // ✅ VERIFICAR SE ESTA PARCELA DEVE IR PARA A NOVA FATURA ATUAL
        const billingInfo = await this.shouldGoToBill(transaction.date, bankId, userId);

        console.log(`📅 Parcela ${transaction.id} (${new Date(transaction.date).toLocaleDateString()}): ${
          billingInfo.shouldGoToBill ? 'DEVE IR PARA FATURA' :
          billingInfo.shouldReduceBankBalance ? 'DEVE REDUZIR SALDO' :
          'DEVE FICAR RESERVADA'
        }`);

        // ✅ PROCESSAR BASEADO NO STATUS ATUAL E DESTINO
        const currentStatus = transaction.installmentStatus;

        if (billingInfo.shouldGoToBill && currentStatus !== INSTALLMENT_STATUS.CURRENT_BILL) {
          // Mover para fatura atual
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: {
              isPaid: false,
              installmentStatus: INSTALLMENT_STATUS.CURRENT_BILL
            }
          });

          totalToAddToBill += transaction.amount;
          processedCount++;
          console.log(`💳 Parcela ${transaction.id} movida para fatura atual: R$ ${transaction.amount}`);
        }
        else if (billingInfo.shouldReduceBankBalance && currentStatus !== INSTALLMENT_STATUS.BANK_BALANCE) {
          // Processar no saldo do banco
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: {
              isPaid: true,
              installmentStatus: INSTALLMENT_STATUS.BANK_BALANCE
            }
          });

          await prisma.bank.update({
            where: { id: bankId },
            data: {
              currentBalance: {
                decrement: transaction.amount
              }
            }
          });

          console.log(`💰 Parcela ${transaction.id} processada no saldo: R$ ${transaction.amount}`);
        }
        else if (billingInfo.shouldStayReserved && currentStatus !== INSTALLMENT_STATUS.RESERVED) {
          // Manter reservada
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: {
              isPaid: true,
              installmentStatus: INSTALLMENT_STATUS.RESERVED
            }
          });

          console.log(`⏳ Parcela ${transaction.id} mantida reservada: R$ ${transaction.amount}`);
        }
      }

      // Atualizar fatura do cartão se houver parcelas
      if (totalToAddToBill > 0) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: totalToAddToBill, // Definir valor total (não incrementar)
            isBillPaid: false
          }
        });

        console.log(`💳 Nova fatura definida: R$ ${totalToAddToBill}`);
      }

      return {
        success: true,
        amountAdded: totalToAddToBill,
        processedCount
      };

    } catch (error) {
      console.error('❌ Erro ao processar parcelas reservadas:', error);
      throw error;
    }
  }

  /**
   * Calcula a próxima data de vencimento da fatura
   */
  getNextBillDueDate(billDueDay, referenceDate = new Date()) {
    const nextDueDate = new Date(referenceDate.getFullYear(), referenceDate.getMonth(), billDueDay);
    
    // Se a data já passou este mês, usar próximo mês
    if (nextDueDate < referenceDate) {
      nextDueDate.setMonth(nextDueDate.getMonth() + 1);
    }
    
    return nextDueDate;
  }

  /**
   * Verifica se uma data está no período de faturamento atual
   */
  isInCurrentBillingPeriod(transactionDate, billDueDay) {
    const transactionDateObj = new Date(transactionDate);
    const currentBillDueDate = this.getNextBillDueDate(billDueDay, transactionDateObj);
    
    // Calcular início do período de faturamento (mês anterior)
    const billingPeriodStart = new Date(currentBillDueDate);
    billingPeriodStart.setMonth(billingPeriodStart.getMonth() - 1);
    
    return transactionDateObj >= billingPeriodStart && transactionDateObj < currentBillDueDate;
  }

  /**
   * Sincroniza todas as transações de cartão com seus ciclos de faturamento
   */
  async syncTransactionsWithBillingCycles(userId) {
    try {
      console.log('🔄 Sincronizando transações com ciclos de faturamento...');
      
      // Buscar todas as transações de cartão de crédito do usuário
      const creditTransactions = await prisma.transaction.findMany({
        where: {
          userId,
          paymentMethod: {
            type: 'CREDIT'
          }
        },
        include: {
          paymentMethod: {
            include: {
              bank: true
            }
          }
        }
      });

      let syncedCount = 0;

      for (const transaction of creditTransactions) {
        if (transaction.paymentMethod.bank && transaction.paymentMethod.bank.billDueDay) {
          const billingInfo = await this.shouldGoToBill(
            transaction.date, 
            transaction.paymentMethod.bank.id, 
            userId
          );

          // Atualizar status da transação se necessário
          const shouldBePaid = billingInfo.shouldReduceBankBalance;
          
          if (transaction.isPaid !== shouldBePaid) {
            await prisma.transaction.update({
              where: { id: transaction.id },
              data: { isPaid: shouldBePaid }
            });
            
            syncedCount++;
            console.log(`🔄 Transação ${transaction.id} sincronizada: isPaid = ${shouldBePaid}`);
          }
        }
      }

      console.log(`✅ ${syncedCount} transações sincronizadas`);
      return { success: true, syncedCount };
      
    } catch (error) {
      console.error('❌ Erro ao sincronizar transações:', error);
      throw error;
    }
  }

  /**
   * Obtém informações detalhadas do ciclo de faturamento de um banco
   */
  async getBillingCycleInfo(bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId },
        include: {
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      if (!bank || !bank.billDueDay) {
        return { hasBillingCycle: false };
      }

      const today = new Date();
      const nextBillDueDate = this.getNextBillDueDate(bank.billDueDay, today);
      const daysUntilDue = Math.ceil((nextBillDueDate - today) / (1000 * 60 * 60 * 24));

      // Calcular período de faturamento atual
      const currentPeriodStart = new Date(nextBillDueDate);
      currentPeriodStart.setMonth(currentPeriodStart.getMonth() - 1);

      return {
        hasBillingCycle: true,
        billDueDay: bank.billDueDay,
        nextBillDueDate,
        daysUntilDue,
        currentPeriodStart,
        currentPeriodEnd: nextBillDueDate,
        paymentMethods: bank.paymentMethods
      };

    } catch (error) {
      console.error('❌ Erro ao obter informações do ciclo:', error);
      throw error;
    }
  }

  /**
   * Sincroniza faturas de um banco específico
   * Move transações que deveriam estar na fatura atual
   */
  async syncBankBills(bankId, userId) {
    try {
      console.log(`🔄 Sincronizando faturas do banco ${bankId}`);

      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank || !bank.billDueDay) {
        console.log('❌ Banco não tem ciclo de faturamento configurado');
        return { success: false, message: 'Banco não tem ciclo de faturamento' };
      }

      // Buscar todas as transações de cartão de crédito deste banco
      const allCreditTransactions = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          userId
        },
        include: {
          paymentMethod: true
        }
      });

      let movedCount = 0;
      let totalMoved = 0;

      for (const transaction of allCreditTransactions) {
        // Verificar se deveria estar na fatura atual
        const billingInfo = await this.shouldGoToBill(transaction.date, bankId, userId);

        if (billingInfo.shouldGoToBill && transaction.isPaid) {
          // Mover para fatura atual
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: { isPaid: false }
          });

          movedCount++;
          totalMoved += transaction.amount;
          console.log(`💳 Transação ${transaction.id} sincronizada para fatura: R$ ${transaction.amount}`);
        }
      }

      // Atualizar faturas dos cartões
      const creditCards = await prisma.paymentMethod.findMany({
        where: {
          bankId,
          type: 'CREDIT',
          userId
        }
      });

      for (const card of creditCards) {
        // Recalcular fatura do cartão
        const cardTransactions = await prisma.transaction.findMany({
          where: {
            paymentMethodId: card.id,
            isPaid: false,
            userId
          }
        });

        const cardBillTotal = cardTransactions.reduce((sum, t) => sum + t.amount, 0);

        await prisma.paymentMethod.update({
          where: { id: card.id },
          data: {
            currentBill: cardBillTotal,
            isBillPaid: cardBillTotal === 0
          }
        });
      }

      console.log(`✅ Sincronização concluída: ${movedCount} transações movidas, R$ ${totalMoved} total`);

      return {
        success: true,
        movedCount,
        totalMoved,
        message: `${movedCount} transações sincronizadas`
      };

    } catch (error) {
      console.error('❌ Erro ao sincronizar faturas:', error);
      throw error;
    }
  }
}

module.exports = new BillingCycleService();
