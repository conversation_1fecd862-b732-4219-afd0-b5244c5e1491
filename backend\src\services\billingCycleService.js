const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class BillingCycleService {
  
  /**
   * Determina se uma transação deve ir para a fatura atual ou próxima fatura
   * baseado no período de faturamento real do cartão de crédito
   */
  async shouldGoToBill(transactionDate, bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank || !bank.billDueDay) {
        // Se não tem dia de fechamento definido, sempre vai para fatura
        return { shouldGoToBill: true, shouldReduceBankBalance: false };
      }

      const transactionDateObj = new Date(transactionDate);
      const today = new Date();

      // ✅ CALCULAR PERÍODO DA FATURA ATUAL (LÓGICA REAL DO CARTÃO)
      // Encontrar o último fechamento que já passou
      let currentBillStart = new Date(today.getFullYear(), today.getMonth(), bank.billDueDay);

      // Se ainda não chegou no fechamento deste mês, a fatura atual começou no mês passado
      if (today.getDate() < bank.billDueDay) {
        currentBillStart.setMonth(currentBillStart.getMonth() - 1);
      }

      // Próximo fechamento (fim da fatura atual)
      const currentBillEnd = new Date(currentBillStart);
      currentBillEnd.setMonth(currentBillEnd.getMonth() + 1);

      // ✅ LÓGICA CORRETA: Se transação está no período da fatura atual, vai para fatura
      // Se está fora do período, reduz saldo do banco
      const isInCurrentBillingPeriod = transactionDateObj >= currentBillStart && transactionDateObj < currentBillEnd;

      console.log(`📅 Análise de faturamento (LÓGICA REAL):`);
      console.log(`   Data da transação: ${transactionDateObj.toLocaleDateString()}`);
      console.log(`   Hoje: ${today.toLocaleDateString()}`);
      console.log(`   Período fatura atual: ${currentBillStart.toLocaleDateString()} até ${currentBillEnd.toLocaleDateString()}`);
      console.log(`   Está no período atual: ${isInCurrentBillingPeriod}`);
      console.log(`   ${isInCurrentBillingPeriod ? 'VAI PARA FATURA' : 'REDUZ SALDO DO BANCO'}`);

      return {
        shouldGoToBill: isInCurrentBillingPeriod,
        shouldReduceBankBalance: !isInCurrentBillingPeriod,
        currentBillStart,
        currentBillEnd,
        bank
      };

    } catch (error) {
      console.error('❌ Erro ao verificar ciclo de faturamento:', error);
      // Em caso de erro, usar comportamento padrão (vai para fatura)
      return { shouldGoToBill: true, shouldReduceBankBalance: false };
    }
  }

  /**
   * Processa o fechamento de faturas baseado nas datas de vencimento
   */
  async processBillingCycles() {
    try {
      console.log('🔄 Processando ciclos de faturamento...');
      
      const today = new Date();
      
      // Buscar bancos com dia de fechamento definido
      const banksWithBilling = await prisma.bank.findMany({
        where: {
          billDueDay: { not: null }
        },
        include: {
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      for (const bank of banksWithBilling) {
        // Verificar se hoje é dia de fechamento
        if (today.getDate() === bank.billDueDay) {
          console.log(`📅 Processando fechamento para banco ${bank.name} (dia ${bank.billDueDay})`);
          
          for (const paymentMethod of bank.paymentMethods) {
            await this.closeBillingCycle(paymentMethod.id, bank.id);
          }
        }
      }

      console.log('✅ Processamento de ciclos de faturamento concluído');
      return { success: true };
      
    } catch (error) {
      console.error('❌ Erro ao processar ciclos de faturamento:', error);
      throw error;
    }
  }

  /**
   * Fecha o ciclo de faturamento de um cartão específico
   */
  async closeBillingCycle(paymentMethodId, bankId) {
    try {
      console.log(`🔄 Fechando ciclo de faturamento para cartão ${paymentMethodId}`);
      
      // Buscar transações não pagas que devem ir para a próxima fatura
      const pendingTransactions = await prisma.transaction.findMany({
        where: {
          paymentMethodId,
          isPaid: true, // Transações que estavam "aguardando"
          date: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) // Deste mês
          }
        }
      });

      let totalToAddToBill = 0;

      for (const transaction of pendingTransactions) {
        // Verificar se deve ir para fatura agora
        const billingInfo = await this.shouldGoToBill(transaction.date, bankId, transaction.userId);
        
        if (billingInfo.shouldGoToBill) {
          // Marcar como não paga (vai para fatura)
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: { isPaid: false }
          });
          
          totalToAddToBill += transaction.amount;
          console.log(`💳 Transação ${transaction.id} movida para fatura: R$ ${transaction.amount}`);
        }
      }

      // Atualizar fatura do cartão
      if (totalToAddToBill > 0) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: {
              increment: totalToAddToBill
            },
            isBillPaid: false
          }
        });
        
        console.log(`💳 Fatura atualizada: +R$ ${totalToAddToBill}`);
      }

      return { success: true, amountAdded: totalToAddToBill };
      
    } catch (error) {
      console.error('❌ Erro ao fechar ciclo de faturamento:', error);
      throw error;
    }
  }

  /**
   * Calcula a próxima data de vencimento da fatura
   */
  getNextBillDueDate(billDueDay, referenceDate = new Date()) {
    const nextDueDate = new Date(referenceDate.getFullYear(), referenceDate.getMonth(), billDueDay);
    
    // Se a data já passou este mês, usar próximo mês
    if (nextDueDate < referenceDate) {
      nextDueDate.setMonth(nextDueDate.getMonth() + 1);
    }
    
    return nextDueDate;
  }

  /**
   * Verifica se uma data está no período de faturamento atual
   */
  isInCurrentBillingPeriod(transactionDate, billDueDay) {
    const transactionDateObj = new Date(transactionDate);
    const currentBillDueDate = this.getNextBillDueDate(billDueDay, transactionDateObj);
    
    // Calcular início do período de faturamento (mês anterior)
    const billingPeriodStart = new Date(currentBillDueDate);
    billingPeriodStart.setMonth(billingPeriodStart.getMonth() - 1);
    
    return transactionDateObj >= billingPeriodStart && transactionDateObj < currentBillDueDate;
  }

  /**
   * Sincroniza todas as transações de cartão com seus ciclos de faturamento
   */
  async syncTransactionsWithBillingCycles(userId) {
    try {
      console.log('🔄 Sincronizando transações com ciclos de faturamento...');
      
      // Buscar todas as transações de cartão de crédito do usuário
      const creditTransactions = await prisma.transaction.findMany({
        where: {
          userId,
          paymentMethod: {
            type: 'CREDIT'
          }
        },
        include: {
          paymentMethod: {
            include: {
              bank: true
            }
          }
        }
      });

      let syncedCount = 0;

      for (const transaction of creditTransactions) {
        if (transaction.paymentMethod.bank && transaction.paymentMethod.bank.billDueDay) {
          const billingInfo = await this.shouldGoToBill(
            transaction.date, 
            transaction.paymentMethod.bank.id, 
            userId
          );

          // Atualizar status da transação se necessário
          const shouldBePaid = billingInfo.shouldReduceBankBalance;
          
          if (transaction.isPaid !== shouldBePaid) {
            await prisma.transaction.update({
              where: { id: transaction.id },
              data: { isPaid: shouldBePaid }
            });
            
            syncedCount++;
            console.log(`🔄 Transação ${transaction.id} sincronizada: isPaid = ${shouldBePaid}`);
          }
        }
      }

      console.log(`✅ ${syncedCount} transações sincronizadas`);
      return { success: true, syncedCount };
      
    } catch (error) {
      console.error('❌ Erro ao sincronizar transações:', error);
      throw error;
    }
  }

  /**
   * Obtém informações detalhadas do ciclo de faturamento de um banco
   */
  async getBillingCycleInfo(bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId },
        include: {
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      if (!bank || !bank.billDueDay) {
        return { hasBillingCycle: false };
      }

      const today = new Date();
      const nextBillDueDate = this.getNextBillDueDate(bank.billDueDay, today);
      const daysUntilDue = Math.ceil((nextBillDueDate - today) / (1000 * 60 * 60 * 24));
      
      // Calcular período de faturamento atual
      const currentPeriodStart = new Date(nextBillDueDate);
      currentPeriodStart.setMonth(currentPeriodStart.getMonth() - 1);

      return {
        hasBillingCycle: true,
        billDueDay: bank.billDueDay,
        nextBillDueDate,
        daysUntilDue,
        currentPeriodStart,
        currentPeriodEnd: nextBillDueDate,
        paymentMethods: bank.paymentMethods
      };
      
    } catch (error) {
      console.error('❌ Erro ao obter informações do ciclo:', error);
      throw error;
    }
  }
}

module.exports = new BillingCycleService();
