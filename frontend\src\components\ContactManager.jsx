import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, User, Mail, Phone, CreditCard, Camera, Search, TrendingUp, TrendingDown, Calendar, DollarSign, BarChart3, Filter, X } from 'lucide-react'
import { transactionContactService } from '../services/transactionContactService'
import toast from 'react-hot-toast'

function ContactManager() {
  const [contacts, setContacts] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingContact, setEditingContact] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedContact, setSelectedContact] = useState(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [contactStats, setContactStats] = useState(null)
  const [statsFilter, setStatsFilter] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear()
  })
  const [newContact, setNewContact] = useState({
    name: '',
    description: '',
    emails: [],
    phones: [],
    pixAccounts: [],
    photo: null
  })
  const [newEmail, setNewEmail] = useState('')
  const [newPhone, setNewPhone] = useState('')
  const [newPixAccount, setNewPixAccount] = useState('')

  useEffect(() => {
    fetchContacts()
  }, [])

  // Escutar evento para abrir contato específico
  useEffect(() => {
    const handleOpenContactDetail = (event) => {
      const contact = event.detail
      handleViewContact(contact)
    }

    window.addEventListener('openContactDetail', handleOpenContactDetail)
    return () => window.removeEventListener('openContactDetail', handleOpenContactDetail)
  }, [])

  const fetchContacts = async () => {
    try {
      setLoading(true)
      const data = await transactionContactService.getContacts()
      setContacts(data)
    } catch (error) {
      console.error('Erro ao carregar contatos:', error)
      toast.error('Erro ao carregar contatos')
    } finally {
      setLoading(false)
    }
  }

  // Funções para gerenciar listas
  const addEmail = () => {
    if (newEmail.trim() && !newContact.emails.includes(newEmail.trim())) {
      setNewContact({ ...newContact, emails: [...newContact.emails, newEmail.trim()] })
      setNewEmail('')
    }
  }

  const removeEmail = (index) => {
    setNewContact({
      ...newContact,
      emails: newContact.emails.filter((_, i) => i !== index)
    })
  }

  const addPhone = () => {
    if (newPhone.trim() && !newContact.phones.includes(newPhone.trim())) {
      setNewContact({ ...newContact, phones: [...newContact.phones, newPhone.trim()] })
      setNewPhone('')
    }
  }

  const removePhone = (index) => {
    setNewContact({
      ...newContact,
      phones: newContact.phones.filter((_, i) => i !== index)
    })
  }

  const addPixAccount = () => {
    if (newPixAccount.trim() && !newContact.pixAccounts.includes(newPixAccount.trim())) {
      setNewContact({ ...newContact, pixAccounts: [...newContact.pixAccounts, newPixAccount.trim()] })
      setNewPixAccount('')
    }
  }

  const removePixAccount = (index) => {
    setNewContact({
      ...newContact,
      pixAccounts: newContact.pixAccounts.filter((_, i) => i !== index)
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      // Converter arrays para strings separadas por vírgula para compatibilidade com backend
      const contactData = {
        ...newContact,
        email: newContact.emails.join(', '),
        phone: newContact.phones.join(', '),
        pixAccount: newContact.pixAccounts.join(', ')
      }

      if (editingContact) {
        await transactionContactService.updateContact(editingContact.id, contactData)
        toast.success('Contato atualizado com sucesso!')
      } else {
        await transactionContactService.createContact(contactData)
        toast.success('Contato criado com sucesso!')
      }
      setShowModal(false)
      setEditingContact(null)
      setNewContact({
        name: '',
        description: '',
        emails: [],
        phones: [],
        pixAccounts: [],
        photo: null
      })
      setNewEmail('')
      setNewPhone('')
      setNewPixAccount('')
      fetchContacts()
    } catch (error) {
      toast.error('Erro ao salvar contato')
    }
  }

  const handleEdit = (contact) => {
    setEditingContact(contact)
    setNewContact({
      name: contact.name,
      description: contact.description || '',
      emails: contact.email ? contact.email.split(', ').filter(e => e.trim()) : [],
      phones: contact.phone ? contact.phone.split(', ').filter(p => p.trim()) : [],
      pixAccounts: contact.pixAccount ? contact.pixAccount.split(', ').filter(p => p.trim()) : [],
      photo: null
    })
    setNewEmail('')
    setNewPhone('')
    setNewPixAccount('')
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja excluir este contato?')) {
      try {
        await transactionContactService.deleteContact(id)
        toast.success('Contato excluído com sucesso!')
        fetchContacts()
      } catch (error) {
        toast.error('Erro ao excluir contato')
      }
    }
  }

  const fetchContactStats = async (contactId) => {
    try {
      const data = await transactionContactService.getContactStats(contactId, statsFilter.month, statsFilter.year)
      setContactStats(data)
    } catch (error) {
      console.error('Erro ao buscar estatísticas do contato:', error)
      toast.error('Erro ao carregar estatísticas')
    }
  }

  const handleViewContact = async (contact) => {
    setSelectedContact(contact)
    setShowDetailModal(true)
    await fetchContactStats(contact.id)
  }

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.phone?.includes(searchTerm)
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Contatos Financeiros</h2>
          <p className="text-gray-600 mt-1">
            Gerencie pessoas e empresas com quem você faz transações
          </p>
        </div>
        <button
          onClick={() => {
            setEditingContact(null)
            setNewContact({
              name: '',
              description: '',
              emails: [],
              phones: [],
              pixAccounts: [],
              photo: null
            })
            setNewEmail('')
            setNewPhone('')
            setNewPixAccount('')
            setShowModal(true)
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Novo Contato
        </button>
      </div>

      {/* Busca */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Buscar contatos..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Lista de Contatos */}
      {filteredContacts.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
          <User className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? 'Nenhum contato encontrado' : 'Nenhum contato cadastrado'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchTerm 
              ? 'Tente buscar com outros termos'
              : 'Adicione pessoas e empresas com quem você faz transações'
            }
          </p>
          {!searchTerm && (
            <button
              onClick={() => setShowModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Adicionar Primeiro Contato
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContacts.map((contact) => (
            <div
              key={contact.id}
              className="bg-gradient-to-br from-white to-gray-50 rounded-2xl border-2 border-gray-200 hover:border-blue-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
            >
              <div className="p-6">
                {/* Avatar e Nome */}
                <div className="flex items-center gap-4 mb-4">
                  <div className="relative">
                    {contact.photo ? (
                      <img
                        src={contact.photo}
                        alt={contact.name}
                        className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                      />
                    ) : (
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white font-bold text-xl">
                          {contact.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-gray-900">{contact.name}</h3>
                    {contact.description && (
                      <p className="text-sm text-gray-600">{contact.description}</p>
                    )}
                  </div>
                </div>

                {/* Informações de Contato */}
                <div className="space-y-3">
                  {contact.email && (
                    <div className="flex items-center gap-3 p-2 bg-white rounded-lg border border-gray-100">
                      <Mail className="h-4 w-4 text-blue-500" />
                      <span className="text-sm text-gray-700 truncate">{contact.email}</span>
                    </div>
                  )}
                  {contact.phone && (
                    <div className="flex items-center gap-3 p-2 bg-white rounded-lg border border-gray-100">
                      <Phone className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-gray-700">{contact.phone}</span>
                    </div>
                  )}
                  {contact.pixAccount && (
                    <div className="flex items-center gap-3 p-2 bg-white rounded-lg border border-gray-100">
                      <CreditCard className="h-4 w-4 text-purple-500" />
                      <span className="text-sm text-gray-700 truncate">{contact.pixAccount}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Ações */}
              <div className="px-6 pb-6">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleViewContact(contact)}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                  >
                    <BarChart3 className="h-4 w-4" />
                    Ver Detalhes
                  </button>
                  <button
                    onClick={() => handleEdit(contact)}
                    className="p-2 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded-lg transition-colors"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(contact.id)}
                    className="p-2 bg-red-100 text-red-600 hover:bg-red-200 rounded-lg transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal de Criação/Edição */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <User className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">
                    {editingContact ? 'Editar Contato' : 'Novo Contato'}
                  </h3>
                  <p className="text-gray-600">
                    {editingContact ? 'Atualize as informações do contato' : 'Adicione um novo contato financeiro'}
                  </p>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="p-6">
              {/* Informações Básicas */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <User className="h-5 w-5 text-blue-500" />
                  Informações Básicas
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Nome */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nome Completo *
                    </label>
                    <input
                      type="text"
                      value={newContact.name}
                      onChange={(e) => setNewContact({ ...newContact, name: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="Ex: João Silva Santos"
                      required
                    />
                  </div>

                  {/* Descrição */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Descrição
                    </label>
                    <input
                      type="text"
                      value={newContact.description}
                      onChange={(e) => setNewContact({ ...newContact, description: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="Ex: Cliente, Fornecedor, Amigo..."
                    />
                  </div>
                </div>
              </div>

              {/* Informações de Contato */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Phone className="h-5 w-5 text-green-500" />
                  Informações de Contato
                </h4>
                {/* Emails */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail className="inline h-4 w-4 mr-1" />
                    Emails
                  </label>
                  <div className="space-y-2">
                    {newContact.emails.map((email, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <input
                          type="email"
                          value={email}
                          readOnly
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                        />
                        <button
                          type="button"
                          onClick={() => removeEmail(index)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                    <div className="flex items-center gap-2">
                      <input
                        type="email"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addEmail())}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Adicionar email..."
                      />
                      <button
                        type="button"
                        onClick={addEmail}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Telefones */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone className="inline h-4 w-4 mr-1" />
                    Telefones
                  </label>
                  <div className="space-y-2">
                    {newContact.phones.map((phone, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <input
                          type="tel"
                          value={phone}
                          readOnly
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                        />
                        <button
                          type="button"
                          onClick={() => removePhone(index)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                    <div className="flex items-center gap-2">
                      <input
                        type="tel"
                        value={newPhone}
                        onChange={(e) => setNewPhone(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addPhone())}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Adicionar telefone..."
                      />
                      <button
                        type="button"
                        onClick={addPhone}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Informações Financeiras */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-purple-500" />
                  Informações Financeiras
                </h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <CreditCard className="inline h-4 w-4 mr-1" />
                    PIX / Contas Bancárias
                  </label>
                  <div className="space-y-2">
                    {newContact.pixAccounts.map((pixAccount, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <input
                          type="text"
                          value={pixAccount}
                          readOnly
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                        />
                        <button
                          type="button"
                          onClick={() => removePixAccount(index)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={newPixAccount}
                        onChange={(e) => setNewPixAccount(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addPixAccount())}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="CPF, email, telefone, chave PIX..."
                      />
                      <button
                        type="button"
                        onClick={addPixAccount}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    💡 Adicione múltiplas informações para facilitar transferências e pagamentos
                  </p>
                </div>
              </div>

              {/* Foto */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Camera className="h-5 w-5 text-orange-500" />
                  Foto do Contato
                </h4>
                <div className="flex items-center gap-6">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Selecionar Foto (Opcional)
                    </label>
                    <div className="relative">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => setNewContact({ ...newContact, photo: e.target.files[0] })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Formatos aceitos: JPG, PNG, GIF (máx. 5MB)
                    </p>
                  </div>
                  <div className="flex-shrink-0">
                    <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center border-2 border-gray-200">
                      <Camera className="h-8 w-8 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Ações */}
              <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false)
                    setEditingContact(null)
                    setNewContact({
                      name: '',
                      description: '',
                      emails: [],
                      phones: [],
                      pixAccounts: [],
                      photo: null
                    })
                    setNewEmail('')
                    setNewPhone('')
                    setNewPixAccount('')
                  }}
                  className="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {editingContact ? 'Atualizar Contato' : 'Criar Contato'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal de Detalhes do Contato */}
      {showDetailModal && selectedContact && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {selectedContact.photo ? (
                    <img
                      src={selectedContact.photo}
                      alt={selectedContact.name}
                      className="w-16 h-16 rounded-full object-cover border-2 border-white shadow-lg"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <span className="text-white font-bold text-2xl">
                        {selectedContact.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">{selectedContact.name}</h3>
                    {selectedContact.description && (
                      <p className="text-gray-600">{selectedContact.description}</p>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowDetailModal(false)
                    setSelectedContact(null)
                    setContactStats(null)
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6">
              {/* Filtros */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-4">
                  <Filter className="h-5 w-5 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Filtrar por período:</span>
                  <select
                    value={statsFilter.month}
                    onChange={(e) => {
                      const newFilter = { ...statsFilter, month: parseInt(e.target.value) }
                      setStatsFilter(newFilter)
                      fetchContactStats(selectedContact.id)
                    }}
                    className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
                  >
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {new Date(2024, i).toLocaleDateString('pt-BR', { month: 'long' })}
                      </option>
                    ))}
                  </select>
                  <select
                    value={statsFilter.year}
                    onChange={(e) => {
                      const newFilter = { ...statsFilter, year: parseInt(e.target.value) }
                      setStatsFilter(newFilter)
                      fetchContactStats(selectedContact.id)
                    }}
                    className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
                  >
                    {Array.from({ length: 5 }, (_, i) => {
                      const year = new Date().getFullYear() - 2 + i
                      return (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      )
                    })}
                  </select>
                </div>
              </div>

              {/* Estatísticas */}
              {contactStats ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-500 rounded-lg">
                        <TrendingUp className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm text-green-600 font-medium">Receitas</p>
                        <p className="text-lg font-bold text-green-800">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(contactStats.totalReceived || 0)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-red-500 rounded-lg">
                        <TrendingDown className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm text-red-600 font-medium">Despesas</p>
                        <p className="text-lg font-bold text-red-800">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(contactStats.totalSent || 0)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-500 rounded-lg">
                        <BarChart3 className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm text-blue-600 font-medium">Transações</p>
                        <p className="text-lg font-bold text-blue-800">
                          {contactStats.totalTransactions || 0}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-500 rounded-lg">
                        <DollarSign className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm text-purple-600 font-medium">Saldo</p>
                        <p className={`text-lg font-bold ${
                          (contactStats.totalReceived - contactStats.totalSent) >= 0
                            ? 'text-green-800'
                            : 'text-red-800'
                        }`}>
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format((contactStats.totalReceived || 0) - (contactStats.totalSent || 0))}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}

              {/* Informações de Contato */}
              <div className="bg-gray-50 rounded-xl p-4">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Informações de Contato</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedContact.email && (
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-blue-500" />
                      <span className="text-gray-700">{selectedContact.email}</span>
                    </div>
                  )}
                  {selectedContact.phone && (
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-green-500" />
                      <span className="text-gray-700">{selectedContact.phone}</span>
                    </div>
                  )}
                  {selectedContact.pixAccount && (
                    <div className="flex items-center gap-3">
                      <CreditCard className="h-5 w-5 text-purple-500" />
                      <span className="text-gray-700">{selectedContact.pixAccount}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ContactManager
