const { PrismaClient } = require('@prisma/client');
const dateService = require('./dateService');
const { INSTALLMENT_STATUS } = require('../constants/status');

const prisma = new PrismaClient();

class InstallmentService {

  /**
   * REGRAS 2, 4, 5: Determina o status correto de uma parcela baseado na data
   */
  async determineInstallmentStatus(installmentDate, bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank || !bank.billDueDay) {
        // Se não tem dia de fechamento, vai direto para saldo
        return {
          status: INSTALLMENT_STATUS.BANK_BALANCE,
          shouldReduceBankBalance: true,
          shouldGoToBill: false,
          shouldStayReserved: false
        };
      }
      //TO DO: Colocar log para ver a data de fechamento da fatura
      const currentDate = await dateService.getCurrentDate();
      const billDueDate = bank.billDueDate || this.calculateBillDueDate(bank.billDueDay, currentDate);

      // Calcular data de fechamento da fatura anterior (1 mês antes)
      const previousBillDueDate = new Date(billDueDate);
      previousBillDueDate.setMonth(previousBillDueDate.getMonth() - 1);

      console.log(`📅 Analisando parcela para ${installmentDate.toLocaleDateString()}:`);
      console.log(`   Fechamento anterior: ${previousBillDueDate.toLocaleDateString()}`);
      console.log(`   Fechamento atual: ${billDueDate.toLocaleDateString()}`);

      // REGRA 2: Data menor que (vencimento - 1 mês) = BANK_BALANCE
      if (installmentDate < previousBillDueDate) {
        console.log(`   ✅ REGRA 2: Data < fechamento anterior → BANK_BALANCE`);
        return {
          status: INSTALLMENT_STATUS.BANK_BALANCE,
          shouldReduceBankBalance: true,
          shouldGoToBill: false,
          shouldStayReserved: false
        };
      }

      // REGRA 4: Data dentro do range (fechamento anterior até fechamento atual) = CURRENT_BILL
      if (installmentDate >= previousBillDueDate && installmentDate <= billDueDate) {
        console.log(`   ✅ REGRA 4: Data no período atual → CURRENT_BILL`);
        return {
          status: INSTALLMENT_STATUS.CURRENT_BILL,
          shouldReduceBankBalance: false,
          shouldGoToBill: true,
          shouldStayReserved: false
        };
      }

      // REGRA 5: Data maior que fechamento atual = RESERVED
      if (installmentDate > billDueDate) {
        console.log(`   ✅ REGRA 5: Data > fechamento atual → RESERVED`);
        return {
          status: INSTALLMENT_STATUS.RESERVED,
          shouldReduceBankBalance: false,
          shouldGoToBill: false,
          shouldStayReserved: true
        };
      }

      // Fallback (não deveria chegar aqui)
      return {
        status: INSTALLMENT_STATUS.PENDING,
        shouldReduceBankBalance: false,
        shouldGoToBill: false,
        shouldStayReserved: false
      };

    } catch (error) {
      console.error('❌ Erro ao determinar status da parcela:', error);
      throw error;
    }
  }

  /**
   * Calcula a data de vencimento da fatura baseada no dia de vencimento
   */
  calculateBillDueDate(billDueDay, currentDate = null) {
    if (!currentDate) {
      currentDate = new Date();
    }

    const billDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), billDueDay);

    // Se a data de vencimento já passou este mês, usar próximo mês
    if (billDueDate <= currentDate) {
      billDueDate.setMonth(billDueDate.getMonth() + 1);
    }

    return billDueDate;
  }

  /**
   * REGRA 6: Processa pagamento da fatura - marca parcelas como PAID
   */
  async processBillPayment(bankId, userId) {
    try {
      console.log(`💳 Processando pagamento da fatura - Banco: ${bankId}`);

      // Buscar todas as transações CURRENT_BILL
      const currentBillTransactions = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          installmentStatus: INSTALLMENT_STATUS.CURRENT_BILL,
          userId
        }
      });

      let processedCount = 0;
      let totalAmount = 0;

      // Marcar todas como PAID
      for (const transaction of currentBillTransactions) {
        await prisma.transaction.update({
          where: { id: transaction.id },
          data: {
            installmentStatus: INSTALLMENT_STATUS.PAID
          }
        });

        processedCount++;
        totalAmount += transaction.amount;
        console.log(`✅ Parcela ${transaction.id} marcada como PAID: R$ ${transaction.amount}`);
      }

      console.log(`💳 ${processedCount} parcelas processadas - Total: R$ ${totalAmount}`);

      return {
        success: true,
        processedCount,
        totalAmount
      };

    } catch (error) {
      console.error('❌ Erro ao processar pagamento da fatura:', error);
      throw error;
    }
  }

  /**
   * Processa parcelas pendentes quando uma fatura é paga
   * Move a próxima parcela para a fatura do mês seguinte
   */
  async processNextInstallments(paymentMethodId, userId) {
    try {
      console.log('🔄 Processando próximas parcelas para cartão:', paymentMethodId);

      // Buscar controles de parcelas ativos para este cartão
      const installmentControls = await prisma.installmentControl.findMany({
        where: {
          paymentMethodId,
          userId,
          isCompleted: false
        },
        include: {
          paymentMethod: true
        }
      });

      console.log(`📋 Encontrados ${installmentControls.length} controles de parcelas ativos`);

      for (const control of installmentControls) {
        // Verificar se ainda há parcelas para processar
        if (control.currentInstallment < control.totalInstallments) {
          const nextInstallmentNumber = control.currentInstallment + 1;

          console.log(`➡️ Processando parcela ${nextInstallmentNumber}/${control.totalInstallments} do controle ${control.id}`);

          // ✅ BUSCAR A PRÓXIMA PARCELA JÁ CRIADA (não criar nova)
          const nextInstallment = await prisma.transaction.findFirst({
            where: {
              parentTransactionId: control.parentTransactionId,
              currentInstallment: nextInstallmentNumber,
              userId: control.userId
            }
          });

          if (nextInstallment) {
            // ✅ VERIFICAR SE DEVE IR PARA FATURA OU REDUZIR SALDO DO BANCO
            const paymentMethod = await prisma.paymentMethod.findFirst({
              where: { id: control.paymentMethodId },
              include: { bank: true }
            });

            if (paymentMethod && paymentMethod.bank) {
              const billingInfo = await billingCycleService.shouldGoToBill(
                nextInstallment.date,
                paymentMethod.bank.id,
                control.userId
              );

              if (billingInfo.shouldReduceBankBalance) {
                // ✅ REDUZIR SALDO DO BANCO
                await prisma.transaction.update({
                  where: { id: nextInstallment.id },
                  data: {
                    isPaid: true, // Marca como paga
                    bankId: paymentMethod.bank.id // Associa ao banco
                  }
                });

                await prisma.bank.update({
                  where: { id: paymentMethod.bank.id },
                  data: {
                    currentBalance: {
                      decrement: control.installmentAmount
                    }
                  }
                });

                console.log(`💰 Parcela ${nextInstallmentNumber} reduziu saldo do banco: R$ ${control.installmentAmount}`);
              } else {
                // ✅ MOVER PARCELA PARA A FATURA
                await prisma.transaction.update({
                  where: { id: nextInstallment.id },
                  data: {
                    isPaid: false // Agora vai para a fatura
                  }
                });

                // Adicionar à fatura do cartão
                await prisma.paymentMethod.update({
                  where: { id: control.paymentMethodId },
                  data: {
                    currentBill: {
                      increment: control.installmentAmount
                    },
                    isBillPaid: false
                  }
                });

                console.log(`💳 Parcela ${nextInstallmentNumber} movida para fatura: R$ ${control.installmentAmount}`);
              }
            }

            // Atualizar o controle
            await prisma.installmentControl.update({
              where: { id: control.id },
              data: {
                currentInstallment: nextInstallmentNumber
              }
            });

            console.log(`✅ Parcela ${nextInstallmentNumber} processada com sucesso`);

            // Se foi a última parcela, marcar como completo
            if (nextInstallmentNumber === control.totalInstallments) {
              await prisma.installmentControl.update({
                where: { id: control.id },
                data: {
                  isCompleted: true
                }
              });
              console.log(`🏁 Controle ${control.id} marcado como completo`);
            }
          } else {
            console.log(`⚠️ Parcela ${nextInstallmentNumber} não encontrada para controle ${control.id}`);
          }
        }
      }

      console.log('✅ Processamento de parcelas concluído');
      return { success: true, processed: installmentControls.length };

    } catch (error) {
      console.error('❌ Erro ao processar próximas parcelas:', error);
      throw error;
    }
  }

  /**
   * Verifica e processa parcelas que devem ser adicionadas automaticamente
   * Baseado na data de vencimento das faturas
   */
  async processScheduledInstallments() {
    try {
      console.log('🕐 Verificando parcelas agendadas...');
      
      const today = new Date();
      
      // Buscar cartões com faturas vencidas (que foram pagas)
      const paymentMethods = await prisma.paymentMethod.findMany({
        where: {
          type: 'CREDIT',
          billDueDate: {
            lt: today // Data de vencimento passou
          },
          isBillPaid: true // Fatura foi paga
        }
      });

      console.log(`💳 Encontrados ${paymentMethods.length} cartões com faturas vencidas e pagas`);

      for (const paymentMethod of paymentMethods) {
        await this.processNextInstallments(paymentMethod.id, paymentMethod.userId);
        
        // Atualizar data de vencimento para o próximo mês
        const nextDueDate = new Date(paymentMethod.billDueDate);
        nextDueDate.setMonth(nextDueDate.getMonth() + 1);
        
        await prisma.paymentMethod.update({
          where: { id: paymentMethod.id },
          data: {
            billDueDate: nextDueDate,
            isBillPaid: false, // Nova fatura
            currentBill: 0 // Resetar fatura (será preenchida pelas parcelas)
          }
        });
      }

      return { success: true };
      
    } catch (error) {
      console.error('❌ Erro ao processar parcelas agendadas:', error);
      throw error;
    }
  }

  /**
   * Lista todas as parcelas de um controle específico
   */
  async getInstallmentsByControl(controlId, userId) {
    try {
      const control = await prisma.installmentControl.findFirst({
        where: { id: controlId, userId },
        include: {
          paymentMethod: true
        }
      });

      if (!control) {
        throw new Error('Controle de parcelas não encontrado');
      }

      // Buscar todas as parcelas criadas para este controle
      const installments = await prisma.transaction.findMany({
        where: {
          parentTransactionId: control.parentTransactionId,
          currentInstallment: { gt: 0 } // Excluir transação de controle
        },
        orderBy: { currentInstallment: 'asc' },
        include: {
          category: true,
          paymentMethod: true
        }
      });

      return {
        control,
        installments,
        summary: {
          totalAmount: control.totalAmount,
          installmentAmount: control.installmentAmount,
          totalInstallments: control.totalInstallments,
          currentInstallment: control.currentInstallment,
          remainingInstallments: control.totalInstallments - control.currentInstallment,
          isCompleted: control.isCompleted
        }
      };
      
    } catch (error) {
      console.error('❌ Erro ao buscar parcelas:', error);
      throw error;
    }
  }

  /**
   * Lista todos os controles de parcelas do usuário
   */
  async getUserInstallmentControls(userId) {
    try {
      const controls = await prisma.installmentControl.findMany({
        where: { userId },
        include: {
          paymentMethod: true
        },
        orderBy: { createdAt: 'desc' }
      });

      return controls;

    } catch (error) {
      console.error('❌ Erro ao buscar controles de parcelas:', error);
      throw error;
    }
  }

  /**
   * Obtém estatísticas de parcelamentos de um banco
   */
  async getInstallmentStats(bankId, userId) {
    try {
      const stats = await prisma.transaction.groupBy({
        by: ['installmentStatus'],
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          userId,
          parentTransactionId: { not: null } // Apenas parcelas
        },
        _count: {
          id: true
        },
        _sum: {
          amount: true
        }
      });

      const result = {
        total: 0,
        totalAmount: 0,
        byStatus: {}
      };

      for (const stat of stats) {
        const status = stat.installmentStatus || 'UNKNOWN';
        result.byStatus[status] = {
          count: stat._count.id,
          amount: stat._sum.amount || 0
        };
        result.total += stat._count.id;
        result.totalAmount += stat._sum.amount || 0;
      }

      return result;

    } catch (error) {
      console.error('❌ Erro ao obter estatísticas:', error);
      throw error;
    }
  }
}

module.exports = new InstallmentService();
