const { PrismaClient } = require('@prisma/client');
const billingCycleService = require('./billingCycleService');
const prisma = new PrismaClient();

class InstallmentService {
  
  /**
   * Processa parcelas pendentes quando uma fatura é paga
   * Move a próxima parcela para a fatura do mês seguinte
   */
  async processNextInstallments(paymentMethodId, userId) {
    try {
      console.log('🔄 Processando próximas parcelas para cartão:', paymentMethodId);

      // Buscar controles de parcelas ativos para este cartão
      const installmentControls = await prisma.installmentControl.findMany({
        where: {
          paymentMethodId,
          userId,
          isCompleted: false
        },
        include: {
          paymentMethod: true
        }
      });

      console.log(`📋 Encontrados ${installmentControls.length} controles de parcelas ativos`);

      for (const control of installmentControls) {
        // Verificar se ainda há parcelas para processar
        if (control.currentInstallment < control.totalInstallments) {
          const nextInstallmentNumber = control.currentInstallment + 1;

          console.log(`➡️ Processando parcela ${nextInstallmentNumber}/${control.totalInstallments} do controle ${control.id}`);

          // ✅ BUSCAR A PRÓXIMA PARCELA JÁ CRIADA (não criar nova)
          const nextInstallment = await prisma.transaction.findFirst({
            where: {
              parentTransactionId: control.parentTransactionId,
              currentInstallment: nextInstallmentNumber,
              userId: control.userId
            }
          });

          if (nextInstallment) {
            // ✅ VERIFICAR SE DEVE IR PARA FATURA OU REDUZIR SALDO DO BANCO
            const paymentMethod = await prisma.paymentMethod.findFirst({
              where: { id: control.paymentMethodId },
              include: { bank: true }
            });

            if (paymentMethod && paymentMethod.bank) {
              const billingInfo = await billingCycleService.shouldGoToBill(
                nextInstallment.date,
                paymentMethod.bank.id,
                control.userId
              );

              if (billingInfo.shouldReduceBankBalance) {
                // ✅ REDUZIR SALDO DO BANCO
                await prisma.transaction.update({
                  where: { id: nextInstallment.id },
                  data: {
                    isPaid: true, // Marca como paga
                    bankId: paymentMethod.bank.id // Associa ao banco
                  }
                });

                await prisma.bank.update({
                  where: { id: paymentMethod.bank.id },
                  data: {
                    currentBalance: {
                      decrement: control.installmentAmount
                    }
                  }
                });

                console.log(`💰 Parcela ${nextInstallmentNumber} reduziu saldo do banco: R$ ${control.installmentAmount}`);
              } else {
                // ✅ MOVER PARCELA PARA A FATURA
                await prisma.transaction.update({
                  where: { id: nextInstallment.id },
                  data: {
                    isPaid: false // Agora vai para a fatura
                  }
                });

                // Adicionar à fatura do cartão
                await prisma.paymentMethod.update({
                  where: { id: control.paymentMethodId },
                  data: {
                    currentBill: {
                      increment: control.installmentAmount
                    },
                    isBillPaid: false
                  }
                });

                console.log(`💳 Parcela ${nextInstallmentNumber} movida para fatura: R$ ${control.installmentAmount}`);
              }
            }

            // Atualizar o controle
            await prisma.installmentControl.update({
              where: { id: control.id },
              data: {
                currentInstallment: nextInstallmentNumber
              }
            });

            console.log(`✅ Parcela ${nextInstallmentNumber} processada com sucesso`);

            // Se foi a última parcela, marcar como completo
            if (nextInstallmentNumber === control.totalInstallments) {
              await prisma.installmentControl.update({
                where: { id: control.id },
                data: {
                  isCompleted: true
                }
              });
              console.log(`🏁 Controle ${control.id} marcado como completo`);
            }
          } else {
            console.log(`⚠️ Parcela ${nextInstallmentNumber} não encontrada para controle ${control.id}`);
          }
        }
      }

      console.log('✅ Processamento de parcelas concluído');
      return { success: true, processed: installmentControls.length };

    } catch (error) {
      console.error('❌ Erro ao processar próximas parcelas:', error);
      throw error;
    }
  }

  /**
   * Verifica e processa parcelas que devem ser adicionadas automaticamente
   * Baseado na data de vencimento das faturas
   */
  async processScheduledInstallments() {
    try {
      console.log('🕐 Verificando parcelas agendadas...');
      
      const today = new Date();
      
      // Buscar cartões com faturas vencidas (que foram pagas)
      const paymentMethods = await prisma.paymentMethod.findMany({
        where: {
          type: 'CREDIT',
          billDueDate: {
            lt: today // Data de vencimento passou
          },
          isBillPaid: true // Fatura foi paga
        }
      });

      console.log(`💳 Encontrados ${paymentMethods.length} cartões com faturas vencidas e pagas`);

      for (const paymentMethod of paymentMethods) {
        await this.processNextInstallments(paymentMethod.id, paymentMethod.userId);
        
        // Atualizar data de vencimento para o próximo mês
        const nextDueDate = new Date(paymentMethod.billDueDate);
        nextDueDate.setMonth(nextDueDate.getMonth() + 1);
        
        await prisma.paymentMethod.update({
          where: { id: paymentMethod.id },
          data: {
            billDueDate: nextDueDate,
            isBillPaid: false, // Nova fatura
            currentBill: 0 // Resetar fatura (será preenchida pelas parcelas)
          }
        });
      }

      return { success: true };
      
    } catch (error) {
      console.error('❌ Erro ao processar parcelas agendadas:', error);
      throw error;
    }
  }

  /**
   * Lista todas as parcelas de um controle específico
   */
  async getInstallmentsByControl(controlId, userId) {
    try {
      const control = await prisma.installmentControl.findFirst({
        where: { id: controlId, userId },
        include: {
          paymentMethod: true
        }
      });

      if (!control) {
        throw new Error('Controle de parcelas não encontrado');
      }

      // Buscar todas as parcelas criadas para este controle
      const installments = await prisma.transaction.findMany({
        where: {
          parentTransactionId: control.parentTransactionId,
          currentInstallment: { gt: 0 } // Excluir transação de controle
        },
        orderBy: { currentInstallment: 'asc' },
        include: {
          category: true,
          paymentMethod: true
        }
      });

      return {
        control,
        installments,
        summary: {
          totalAmount: control.totalAmount,
          installmentAmount: control.installmentAmount,
          totalInstallments: control.totalInstallments,
          currentInstallment: control.currentInstallment,
          remainingInstallments: control.totalInstallments - control.currentInstallment,
          isCompleted: control.isCompleted
        }
      };
      
    } catch (error) {
      console.error('❌ Erro ao buscar parcelas:', error);
      throw error;
    }
  }

  /**
   * Lista todos os controles de parcelas do usuário
   */
  async getUserInstallmentControls(userId) {
    try {
      const controls = await prisma.installmentControl.findMany({
        where: { userId },
        include: {
          paymentMethod: true
        },
        orderBy: { createdAt: 'desc' }
      });

      return controls;
      
    } catch (error) {
      console.error('❌ Erro ao buscar controles de parcelas:', error);
      throw error;
    }
  }
}

module.exports = new InstallmentService();
