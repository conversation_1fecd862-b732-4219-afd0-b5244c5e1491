const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar notificações
router.get('/', async (req, res) => {
  try {
    const notifications = await prisma.notification.findMany({
      where: { userId: req.user.id },
      orderBy: [
        { isRead: 'asc' }, // Não lidas primeiro
        { createdAt: 'desc' } // Mais recentes primeiro
      ]
    });

    res.json(notifications);
  } catch (error) {
    console.error('Erro ao buscar notificações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter contagem de não lidas
router.get('/unreadcount', async (req, res) => {
  try {
    const count = await prisma.notification.count({
      where: { 
        userId: req.user.id,
        isRead: false
      }
    });

    res.json({ count });
  } catch (error) {
    console.error('Erro ao contar notificações não lidas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Marcar notificação como lida
router.patch('/:id/read', async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await prisma.notification.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!notification) {
      return res.status(404).json({ error: 'Notificação não encontrada' });
    }

    const updatedNotification = await prisma.notification.update({
      where: { id },
      data: { isRead: true }
    });

    res.json(updatedNotification);
  } catch (error) {
    console.error('Erro ao marcar notificação como lida:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Marcar todas como lidas
router.patch('/read-all', async (req, res) => {
  try {
    await prisma.notification.updateMany({
      where: { 
        userId: req.user.id,
        isRead: false
      },
      data: { isRead: true }
    });

    res.json({ message: 'Todas as notificações foram marcadas como lidas' });
  } catch (error) {
    console.error('Erro ao marcar todas as notificações como lidas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar notificação
router.post('/', async (req, res) => {
  try {
    const { title, message, type, priority, amount, entityId, entityType } = req.body;

    if (!title || !message || !type) {
      return res.status(400).json({ error: 'Título, mensagem e tipo são obrigatórios' });
    }

    const notification = await prisma.notification.create({
      data: {
        title,
        message,
        type,
        priority: priority || 'MEDIUM',
        amount: amount || null,
        entityId: entityId || null,
        entityType: entityType || null,
        userId: req.user.id
      }
    });

    res.status(201).json(notification);
  } catch (error) {
    console.error('Erro ao criar notificação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar notificação
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await prisma.notification.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!notification) {
      return res.status(404).json({ error: 'Notificação não encontrada' });
    }

    await prisma.notification.delete({
      where: { id }
    });

    res.json({ message: 'Notificação deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar notificação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Gerar notificações automáticas (chamado por cron job ou manualmente)
router.post('/generate', async (req, res) => {
  try {
    const userId = req.user.id;
    const notifications = [];

    // 1. Verificar empréstimos em atraso
    const overdueLoans = await prisma.loan.findMany({
      where: {
        userId,
        status: 'ACTIVE',
        expectedEndDate: {
          lt: new Date()
        }
      },
      include: {
        contact: true
      }
    });

    for (const loan of overdueLoans) {
      notifications.push({
        title: 'Empréstimo em Atraso',
        message: `O empréstimo para ${loan.contact.name} está em atraso desde ${new Date(loan.expectedEndDate).toLocaleDateString('pt-BR')}`,
        type: 'OVERDUE_LOAN',
        priority: 'HIGH',
        amount: loan.totalAmount,
        entityId: loan.id,
        entityType: 'loan',
        userId
      });
    }

    // 2. Verificar empréstimos próximos do vencimento (próximos 7 dias)
    const upcomingLoans = await prisma.loan.findMany({
      where: {
        userId,
        status: 'ACTIVE',
        expectedEndDate: {
          gte: new Date(),
          lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        }
      },
      include: {
        contact: true
      }
    });

    for (const loan of upcomingLoans) {
      notifications.push({
        title: 'Empréstimo Próximo do Vencimento',
        message: `O empréstimo para ${loan.contact.name} vence em ${new Date(loan.expectedEndDate).toLocaleDateString('pt-BR')}`,
        type: 'UPCOMING_LOAN',
        priority: 'MEDIUM',
        amount: loan.totalAmount,
        entityId: loan.id,
        entityType: 'loan',
        userId
      });
    }

    // 3. Verificar faturas de cartão em atraso
    const overdueBills = await prisma.creditCardBill.findMany({
      where: {
        userId,
        isPaid: false,
        dueDate: {
          lt: new Date()
        }
      },
      include: {
        paymentMethod: true
      }
    });

    for (const bill of overdueBills) {
      notifications.push({
        title: 'Fatura em Atraso',
        message: `A fatura do ${bill.paymentMethod.name} está em atraso desde ${new Date(bill.dueDate).toLocaleDateString('pt-BR')}`,
        type: 'OVERDUE_BILL',
        priority: 'HIGH',
        amount: bill.amount,
        entityId: bill.id,
        entityType: 'bill',
        userId
      });
    }

    // 4. Verificar faturas próximas do vencimento (próximos 3 dias)
    const upcomingBills = await prisma.creditCardBill.findMany({
      where: {
        userId,
        isPaid: false,
        dueDate: {
          gte: new Date(),
          lte: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
        }
      },
      include: {
        paymentMethod: true
      }
    });

    for (const bill of upcomingBills) {
      notifications.push({
        title: 'Fatura Próxima do Vencimento',
        message: `A fatura do ${bill.paymentMethod.name} vence em ${new Date(bill.dueDate).toLocaleDateString('pt-BR')}`,
        type: 'UPCOMING_BILL',
        priority: 'MEDIUM',
        amount: bill.amount,
        entityId: bill.id,
        entityType: 'bill',
        userId
      });
    }

    // 5. Verificar saldos baixos (menos de R$ 100)
    const lowBalanceBanks = await prisma.bank.findMany({
      where: {
        userId,
        currentBalance: {
          lt: 100,
          gt: 0
        },
        isVisible: true
      }
    });

    for (const bank of lowBalanceBanks) {
      notifications.push({
        title: 'Saldo Baixo',
        message: `O saldo da conta ${bank.name} está baixo: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(bank.currentBalance)}`,
        type: 'LOW_BALANCE',
        priority: 'LOW',
        amount: bank.currentBalance,
        entityId: bank.id,
        entityType: 'bank',
        userId
      });
    }

    // Criar notificações que ainda não existem
    for (const notificationData of notifications) {
      const existing = await prisma.notification.findFirst({
        where: {
          userId,
          type: notificationData.type,
          entityId: notificationData.entityId,
          isRead: false
        }
      });

      if (!existing) {
        await prisma.notification.create({
          data: notificationData
        });
      }
    }

    res.json({ 
      message: 'Notificações geradas com sucesso',
      count: notifications.length
    });
  } catch (error) {
    console.error('Erro ao gerar notificações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
