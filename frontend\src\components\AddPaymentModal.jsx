import React, { useState } from 'react'
import { X, Plus, DollarSign, Calendar, AlertCircle } from 'lucide-react'
import { loanService } from '../services/loanService'
import toast from 'react-hot-toast'

function AddPaymentModal({ isOpen, onClose, loan, remainingInfo, onSuccess }) {
  const [amount, setAmount] = useState('')
  const [dueDate, setDueDate] = useState('')
  const [loading, setLoading] = useState(false)

  if (!isOpen || !remainingInfo) return null

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!amount || !dueDate) {
      toast.error('Preencha todos os campos')
      return
    }

    const amountValue = parseFloat(amount)
    if (amountValue <= 0) {
      toast.error('Valor deve ser maior que zero')
      return
    }

    if (amountValue > remainingInfo.remainingAmount) {
      toast.error(`Valor não pode ser maior que o restante (R$ ${remainingInfo.remainingAmount.toFixed(2)})`)
      return
    }

    setLoading(true)
    try {
      await loanService.addPayment(loan.id, {
        amount: amountValue,
        dueDate
      })
      
      toast.success('Parcela adicionada com sucesso!')
      onSuccess()
      onClose()
    } catch (error) {
      toast.error('Erro ao adicionar parcela')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <Plus className="h-5 w-5" />
              </div>
              <div>
                <h2 className="text-xl font-bold">Adicionar Parcela</h2>
                <p className="text-blue-100 text-sm">{loan?.title}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Body */}
        <div className="p-6">
          {/* Informações do Empréstimo */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-blue-500" />
              Informações do Empréstimo
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Valor Total:</span>
                <p className="font-semibold">{formatCurrency(remainingInfo.totalAmount)}</p>
              </div>
              <div>
                <span className="text-gray-600">Valor Pago:</span>
                <p className="font-semibold text-green-600">{formatCurrency(remainingInfo.totalPaid)}</p>
              </div>
              <div>
                <span className="text-gray-600">Valor Restante:</span>
                <p className="font-semibold text-red-600">{formatCurrency(remainingInfo.remainingAmount)}</p>
              </div>
              <div>
                <span className="text-gray-600">Parcelas:</span>
                <p className="font-semibold">{remainingInfo.paidInstallments}/{remainingInfo.totalInstallments}</p>
              </div>
            </div>
          </div>

          {/* Formulário */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor da Parcela
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  max={remainingInfo.remainingAmount}
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0,00"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Máximo: {formatCurrency(remainingInfo.remainingAmount)}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data de Vencimento
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Botões */}
            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Adicionando...' : 'Adicionar Parcela'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default AddPaymentModal
