const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const installmentService = require('../services/installmentService');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar todos os controles de parcelas do usuário
router.get('/', async (req, res) => {
  try {
    const controls = await installmentService.getUserInstallmentControls(req.user.id);
    res.json(controls);
  } catch (error) {
    console.error('Erro ao buscar controles de parcelas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar detalhes de um controle específico
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const details = await installmentService.getInstallmentsByControl(id, req.user.id);
    res.json(details);
  } catch (error) {
    console.error('Erro ao buscar detalhes do controle:', error);
    if (error.message === 'Controle de parcelas não encontrado') {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// Processar próximas parcelas manualmente (para testes)
router.post('/process-next/:paymentMethodId', async (req, res) => {
  try {
    const { paymentMethodId } = req.params;
    
    // Verificar se o método de pagamento pertence ao usuário
    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: { id: paymentMethodId, userId: req.user.id }
    });

    if (!paymentMethod) {
      return res.status(404).json({ error: 'Método de pagamento não encontrado' });
    }

    const result = await installmentService.processNextInstallments(paymentMethodId, req.user.id);
    res.json(result);
  } catch (error) {
    console.error('Erro ao processar próximas parcelas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar parcelas de uma transação específica (compatibilidade com sistema antigo)
router.get('/transaction/:transactionId/installments', async (req, res) => {
  try {
    const { transactionId } = req.params;
    
    // Buscar a transação
    const transaction = await prisma.transaction.findFirst({
      where: { id: transactionId, userId: req.user.id },
      include: {
        category: true,
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!transaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    let installments = [];

    // Se a transação tem parcelas (installments > 1)
    if (transaction.installments > 1) {
      // Se é uma transação pai (currentInstallment = 0), buscar controle e parcelas
      if (transaction.currentInstallment === 0) {
        const control = await prisma.installmentControl.findFirst({
          where: { parentTransactionId: transactionId, userId: req.user.id }
        });

        if (control) {
          // Buscar todas as parcelas criadas
          installments = await prisma.transaction.findMany({
            where: {
              parentTransactionId: transactionId,
              currentInstallment: { gt: 0 }, // Excluir transação de controle
              userId: req.user.id
            },
            orderBy: { currentInstallment: 'asc' },
            include: {
              category: true,
              paymentMethod: {
                include: {
                  bank: true
                }
              }
            }
          });

          // Adicionar informações do controle
          const summary = {
            totalAmount: control.totalAmount,
            installmentAmount: control.installmentAmount,
            totalInstallments: control.totalInstallments,
            currentInstallment: control.currentInstallment,
            remainingInstallments: control.totalInstallments - control.currentInstallment,
            isCompleted: control.isCompleted,
            nextInstallmentDate: control.currentInstallment < control.totalInstallments 
              ? new Date(new Date(control.startDate).setMonth(new Date(control.startDate).getMonth() + control.currentInstallment))
              : null
          };

          return res.json({ installments, summary, control });
        }
      } else {
        // Se é uma parcela filha, buscar todas as parcelas irmãs
        installments = await prisma.transaction.findMany({
          where: {
            parentTransactionId: transaction.parentTransactionId,
            currentInstallment: { gt: 0 },
            userId: req.user.id
          },
          orderBy: { currentInstallment: 'asc' },
          include: {
            category: true,
            paymentMethod: {
              include: {
                bank: true
              }
            }
          }
        });
      }
    } else {
      // Se não tem parcelas, retornar apenas a transação atual
      installments = [transaction];
    }

    res.json({ installments });
  } catch (error) {
    console.error('Erro ao buscar parcelas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Cancelar um controle de parcelas (marcar como completo)
router.patch('/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;
    
    const control = await prisma.installmentControl.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!control) {
      return res.status(404).json({ error: 'Controle de parcelas não encontrado' });
    }

    if (control.isCompleted) {
      return res.status(400).json({ error: 'Controle já está completo' });
    }

    await prisma.installmentControl.update({
      where: { id },
      data: { isCompleted: true }
    });

    res.json({ message: 'Controle de parcelas cancelado com sucesso' });
  } catch (error) {
    console.error('Erro ao cancelar controle:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Estatísticas de parcelas do usuário
router.get('/stats/summary', async (req, res) => {
  try {
    const userId = req.user.id;

    // Controles ativos
    const activeControls = await prisma.installmentControl.count({
      where: { userId, isCompleted: false }
    });

    // Controles completos
    const completedControls = await prisma.installmentControl.count({
      where: { userId, isCompleted: true }
    });

    // Valor total em parcelas ativas
    const activeControlsData = await prisma.installmentControl.findMany({
      where: { userId, isCompleted: false },
      select: { totalAmount: true, installmentAmount: true, currentInstallment: true, totalInstallments: true }
    });

    const totalActiveAmount = activeControlsData.reduce((sum, control) => sum + control.totalAmount, 0);
    const totalRemainingAmount = activeControlsData.reduce((sum, control) => {
      const remaining = (control.totalInstallments - control.currentInstallment) * control.installmentAmount;
      return sum + remaining;
    }, 0);

    // Próximas parcelas (próximo mês)
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    
    const upcomingInstallments = activeControlsData.filter(control => 
      control.currentInstallment < control.totalInstallments
    ).length;

    res.json({
      activeControls,
      completedControls,
      totalActiveAmount,
      totalRemainingAmount,
      upcomingInstallments,
      averageInstallmentValue: activeControls > 0 ? totalRemainingAmount / upcomingInstallments : 0
    });
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
