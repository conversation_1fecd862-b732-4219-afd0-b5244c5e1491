// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}



model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  transactions      Transaction[]
  categories        Category[]
  dashboardLayouts  DashboardLayout[]
  dashboardCards    DashboardCard[]
  dashboardProfiles DashboardProfile[]
  banks             Bank[]
  paymentMethods    PaymentMethod[]
  savings           Savings[]
  creditCardBills   CreditCardBill[]
  subscriptions     Subscription[]
  contacts          Contact[]
  loans             Loan[]
  loanPayments      LoanPayment[]
  tags              Tag[]
  routines          Routine[]
  notifications     Notification[]
  transactionTemplates TransactionTemplate[]
  transactionContacts TransactionContact[]
  piggyBanks        PiggyBank[]
  installmentControls InstallmentControl[]
  billHistory       BillHistory[]

  @@map("users")
}

model Category {
  id           String     @id @default(cuid())
  name         String
  color        String     @default("#3B82F6")
  icon         String     @default("💰")
  userId       String
  parentId     String?    // ID da categoria pai
  isSubcategory Boolean   @default(false)

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent        Category?      @relation("SubCategories", fields: [parentId], references: [id], onDelete: SetNull)
  subcategories Category[]     @relation("SubCategories")
  transactions  Transaction[]
  subscriptions Subscription[]
  routineItems  RoutineItem[]
  transactionTemplates TransactionTemplate[]

  @@map("categories")
}

model Transaction {
  id              String   @id @default(cuid())
  description     String
  amount          Float
  type            String // INCOME, EXPENSE, INVESTMENT, LOAN
  date            DateTime @default(now())
  categoryId      String?
  userId          String
  bankId          String?
  paymentMethodId String?
  transactionContactId String? // Contato relacionado à transação
  receiptUrl      String? // URL do comprovante no Cloudinary
  installments    Int      @default(1) // Número de parcelas
  currentInstallment Int   @default(1) // Parcela atual
  parentTransactionId String? // ID da transação pai (para parcelas)
  installmentStatus String? // Status: PENDING, CURRENT_BILL, RESERVED, BANK_BALANCE, PAID, OVERDUE
  isPaid          Boolean  @default(false) // Se a transação foi paga (para cartões de crédito)
  loanId          String? // ID do empréstimo relacionado
  loanPaymentId   String? // ID da parcela do empréstimo relacionada
  isFromRoutine   Boolean  @default(false) // Se a transação foi criada por uma rotina
  isFromTemplate  Boolean  @default(false) // Se a transação foi criada por um template
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  category      Category?      @relation(fields: [categoryId], references: [id])
  bank          Bank?          @relation(fields: [bankId], references: [id])
  paymentMethod PaymentMethod? @relation(fields: [paymentMethodId], references: [id])
  transactionContact TransactionContact? @relation(fields: [transactionContactId], references: [id])
  loan          Loan?          @relation(fields: [loanId], references: [id])
  loanPayment   LoanPayment?   @relation(fields: [loanPaymentId], references: [id])
  tags          Tag[]          @relation("TransactionTags")

  @@map("transactions")
}

model DashboardLayout {
  id        String   @id @default(cuid())
  userId    String
  page      String // 'dashboard' ou 'analytics'
  layout    String // JSON com configurações do layout
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, page])
  @@map("dashboard_layouts")
}

model DashboardCard {
  id            String   @id @default(cuid())
  userId        String
  profileId     String?
  title         String
  type          String // 'numeric', 'pie', 'table', 'chart'
  position      String // JSON com x, y, w, h
  config        String // JSON com configurações específicas do card
  categories    String // JSON com IDs das categorias conectadas
  dataSource    String   @default("categories") // 'categories', 'banks', 'savings', 'subscriptions', 'bills'
  selectedItems String   @default("[]") // JSON com IDs dos itens selecionados
  visible       Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  user    User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  profile DashboardProfile? @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("dashboard_cards")
}

model DashboardProfile {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user  User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  cards DashboardCard[]

  @@map("dashboard_profiles")
}

model Bank {
  id             String   @id @default(cuid())
  name           String
  icon           String   @default("🏦")
  color          String   @default("#3B82F6")
  initialBalance Float    @default(0)
  currentBalance Float    @default(0)
  isVisible      Boolean  @default(true)
  billDueDay     Int?     // Dia de fechamento da fatura (para cartões)
  creditLimit    Float?   @default(0) // Limite total do cartão de crédito
  availableLimit Float?   @default(0) // Limite disponível atual
  isLimitLocked  Boolean  @default(false) // Se o limite está travado
  billStatus     String   @default("PENDING") // Status: PENDING, PAID, OVERDUE
  currentBillAmount Float @default(0) // Valor atual da fatura
  billDueDate    DateTime? // Data de vencimento da fatura atual
  userId         String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions   Transaction[]
  paymentMethods PaymentMethod[]
  savings        Savings[]
  loans          Loan[]
  loanPayments   LoanPayment[]
  routineItems   RoutineItem[]
  transactionTemplates TransactionTemplate[]
  piggyBanks     PiggyBank[]
  billHistory    BillHistory[]
  paymentHistory BillHistory[] @relation("PaymentBank")

  @@map("banks")
}

model PaymentMethod {
  id              String   @id @default(cuid())
  name            String
  icon            String   @default("💳")
  color           String   @default("#10B981")
  type            String // 'CREDIT', 'DEBIT', 'CASH', 'PIX', 'TRANSFER', 'OTHER'
  bankId          String?       // Conectado a um banco específico
  billDueDay      Int?     // Dia do vencimento da fatura (1-31)
  currentBill     Float    @default(0) // Valor atual da fatura
  billDueDate     DateTime? // Próxima data de vencimento
  isBillPaid      Boolean  @default(true) // Status da fatura atual
  transactionLimit Float? // Limite por transação
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  bank          Bank?          @relation(fields: [bankId], references: [id], onDelete: SetNull)
  transactions  Transaction[]
  bills         CreditCardBill[]
  subscriptions Subscription[]
  installmentControls InstallmentControl[]

  @@map("payment_methods")
}

model Savings {
  id            String   @id @default(cuid())
  name          String
  description   String?
  targetAmount  Float    @default(0)
  currentAmount Float    @default(0)
  icon          String   @default("🐷")
  color         String   @default("#22C55E")
  isLocked      Boolean  @default(false)
  cdiRate       Float    @default(0) // Taxa CDI atual
  lastCdiUpdate DateTime @default(now()) // Última atualização do CDI
  bankId        String
  userId        String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  bank Bank @relation(fields: [bankId], references: [id], onDelete: Cascade)

  @@map("savings")
}

model CreditCardBill {
  id              String   @id @default(cuid())
  amount          Float    @default(0)
  dueDate         DateTime
  isPaid          Boolean  @default(false)
  paidAt          DateTime?
  paymentMethodId String
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  paymentMethod PaymentMethod @relation(fields: [paymentMethodId], references: [id], onDelete: Cascade)

  @@map("credit_card_bills")
}

model Subscription {
  id              String   @id @default(cuid())
  name            String
  description     String?
  amount          Float
  startDate       DateTime
  nextBillDate    DateTime
  billingDay      Int      // Dia do mês para cobrança (1-31)
  lastBilledMonth String?  // Formato: "YYYY-MM" para controlar cobrança mensal
  status          String @default("PENDING") // Status: PENDING, BILLED, PAID, OVERDUE, CANCELLED
  isActive        Boolean  @default(true)
  paymentMethodId String
  categoryId      String?
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  paymentMethod PaymentMethod  @relation(fields: [paymentMethodId], references: [id], onDelete: Cascade)
  category      Category?      @relation(fields: [categoryId], references: [id])

  @@map("subscriptions")
}

model Contact {
  id          String   @id @default(cuid())
  name        String
  email       String?
  phone       String?
  photoUrl    String?  // URL da foto no Cloudinary
  notes       String?
  status      String   @default("NEUTRAL") // GOOD, NEUTRAL, BAD
  totalLoans  Int      @default(0) // Total de empréstimos
  paidOnTime  Int      @default(0) // Pagamentos em dia
  latePayments Int     @default(0) // Pagamentos atrasados
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  loans Loan[]

  @@map("contacts")
}

model TransactionContact {
  id          String   @id @default(cuid())
  name        String
  description String?
  email       String?
  phone       String?
  pixAccount  String?
  photo       String?
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]
  routineItems RoutineItem[]
  transactionTemplates TransactionTemplate[]

  @@map("transaction_contacts")
}

model Loan {
  id              String   @id @default(cuid())
  contactId       String
  title           String   // Nome/descrição do empréstimo
  type            String   // LOAN_GIVEN (emprestei), LOAN_RECEIVED (peguei emprestado)
  loanType        String   // MONEY (dinheiro), CREDIT_CARD (compra no cartão), OTHER
  totalAmount     Float    // Valor total
  installmentAmount Float? // Valor da parcela (se parcelado)
  installments    Int      @default(1) // Número de parcelas
  paidInstallments Int     @default(0) // Parcelas pagas
  interestRate    Float    @default(0) // Taxa de juros (%)
  startDate       DateTime // Data do empréstimo
  expectedEndDate DateTime? // Data esperada de quitação
  status          String   @default("ACTIVE") // ACTIVE, COMPLETED, OVERDUE, CANCELLED
  bankId          String?  // Banco usado na transação
  receiptUrl      String?  // Comprovante do empréstimo
  notes           String?
  userId          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  contact      Contact       @relation(fields: [contactId], references: [id], onDelete: Cascade)
  bank         Bank?         @relation(fields: [bankId], references: [id])
  payments     LoanPayment[]
  transactions Transaction[] // Transações relacionadas ao empréstimo

  @@map("loans")
}

model LoanPayment {
  id                String    @id @default(cuid())
  loanId            String
  amount            Float     // Valor da parcela
  paymentDate       DateTime? // Data do pagamento (quando pago)
  dueDate           DateTime  // Data de vencimento
  installmentNumber Int       // Número da parcela (1, 2, 3...)
  isPaid            Boolean   @default(false)
  isLate            Boolean   @default(false) // Se foi pago com atraso
  bankId            String?   // Banco usado no pagamento
  receiptUrl        String?   // Comprovante do pagamento
  notes             String?
  userId            String
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  loan         Loan          @relation(fields: [loanId], references: [id], onDelete: Cascade)
  bank         Bank?         @relation(fields: [bankId], references: [id])
  transactions Transaction[] // Transações relacionadas à parcela

  @@map("loan_payments")
}

model Tag {
  id        String   @id @default(cuid())
  name      String
  color     String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[] @relation("TransactionTags")

  @@map("tags")
}

model Routine {
  id          String   @id @default(cuid())
  name        String
  description String?
  executionDay Int     // Dia do mês para executar (1-31)
  isActive    Boolean  @default(true)
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user        User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  items       RoutineItem[]

  @@map("routines")
}

model RoutineItem {
  id          String   @id @default(cuid())
  name        String
  type        String // INCOME, EXPENSE, INVESTMENT, LOAN
  description String?
  transactionContactId String? // Contato relacionado
  routineId   String
  categoryId  String
  bankId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  routine     Routine     @relation(fields: [routineId], references: [id], onDelete: Cascade)
  category    Category    @relation(fields: [categoryId], references: [id])
  bank        Bank        @relation(fields: [bankId], references: [id])
  transactionContact TransactionContact? @relation(fields: [transactionContactId], references: [id])

  @@map("routine_items")
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String
  type      String   // OVERDUE_BILL, UPCOMING_BILL, OVERDUE_LOAN, UPCOMING_LOAN, CREDIT_CARD_BILL, ROUTINE_EXECUTION, LOW_BALANCE
  priority  String   @default("MEDIUM") // LOW, MEDIUM, HIGH
  isRead    Boolean  @default(false)
  amount    Float?   // Valor relacionado (opcional)
  entityId  String?  // ID da entidade relacionada (empréstimo, fatura, etc)
  entityType String? // Tipo da entidade (loan, bill, routine, etc)
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model TransactionTemplate {
  id          String   @id @default(cuid())
  name        String   // Nome do template (ex: "Pizza da Sexta")
  description String?  // Descrição opcional
  type        String   // INCOME, EXPENSE, INVESTMENT, LOAN
  categoryId  String
  bankId      String
  transactionContactId String? // Contato relacionado
  tags        String?  // Tags separadas por vírgula
  isActive    Boolean  @default(true)
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  category Category @relation(fields: [categoryId], references: [id])
  bank     Bank     @relation(fields: [bankId], references: [id])
  transactionContact TransactionContact? @relation(fields: [transactionContactId], references: [id])
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transaction_templates")
}

model PiggyBank {
  id            String   @id @default(cuid())
  name          String   // Nome do cofrinho
  description   String?  // Descrição opcional
  icon          String   @default("🐷") // Ícone do cofrinho
  color         String   @default("#EC4899") // Cor do cofrinho
  currentAmount Float    @default(0) // Valor atual
  targetAmount  Float?   // Meta opcional
  bankId        String   // Banco vinculado para CDI
  cdiRate       Float    @default(0) // Taxa CDI atual
  lastCdiUpdate DateTime @default(now()) // Última atualização do CDI
  isActive      Boolean  @default(true)
  userId        String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  bank Bank @relation(fields: [bankId], references: [id])
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("piggy_banks")
}

model InstallmentControl {
  id                String   @id @default(cuid())
  parentTransactionId String // ID da transação pai/controle
  paymentMethodId   String   // ID do cartão de crédito
  totalAmount       Float    // Valor total da compra
  installmentAmount Float    // Valor de cada parcela
  totalInstallments Int      // Total de parcelas
  currentInstallment Int     // Parcela atual (próxima a ser adicionada na fatura)
  description       String   // Descrição da compra
  startDate         DateTime // Data da primeira parcela
  isCompleted       Boolean  @default(false) // Se todas as parcelas foram processadas
  userId            String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  paymentMethod PaymentMethod @relation(fields: [paymentMethodId], references: [id], onDelete: Cascade)
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("installment_controls")
}

model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_configs")
}

model BillHistory {
  id          String   @id @default(cuid())
  bankId      String   // Banco da fatura
  amount      Float    // Valor da fatura paga
  dueDate     DateTime // Data de vencimento
  paidDate    DateTime // Data do pagamento
  paymentBankId String // Banco usado para pagamento
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  bank        Bank @relation(fields: [bankId], references: [id])
  paymentBank Bank @relation("PaymentBank", fields: [paymentBankId], references: [id])
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("bill_history")
}

// Status constants for reference:
// SubscriptionStatus: PENDING, BILLED, PAID, OVERDUE, CANCELLED
// InstallmentStatus: PENDING, CURRENT_BILL, RESERVED, BANK_BALANCE, PAID, OVERDUE
// BillStatus: PENDING, PAID, OVERDUE
