import React, { useState, useEffect } from 'react'
import { X, User, Mail, Phone, DollarSign, Calendar, CheckCircle, Clock, AlertCircle, Plus, Eye, Download, Trash2, Edit3, Save, ChevronDown, ChevronRight } from 'lucide-react'
import { contactService, loanService, loanUtils } from '../services/loanService'
import PaymentModal from './PaymentModal'
import AddPaymentModal from './AddPaymentModal'
import toast from 'react-hot-toast'

function ContactDetailModal({ isOpen, onClose, contact, banks, onSuccess }) {
  const [loading, setLoading] = useState(false)
  const [contactData, setContactData] = useState(null)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState(null)
  const [selectedLoan, setSelectedLoan] = useState(null)
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false)
  const [remainingInfo, setRemainingInfo] = useState(null)
  const [editingStatus, setEditingStatus] = useState(false)
  const [newStatus, setNewStatus] = useState('')
  const [editingPayments, setEditingPayments] = useState({})
  const [showPaymentTable, setShowPaymentTable] = useState({})

  // Estados para filtros e minimização (por padrão todos minimizados)
  const [minimizedLoans, setMinimizedLoans] = useState(new Set())
  const [loanFilters, setLoanFilters] = useState({
    search: '',
    status: '',
    type: '',
    dateFrom: '',
    dateTo: ''
  })

  useEffect(() => {
    if (isOpen && contact) {
      fetchContactDetails()
    }
  }, [isOpen, contact])

  const fetchContactDetails = async () => {
    try {
      setLoading(true)
      const data = await contactService.getContact(contact.id)
      setContactData(data)

      // Minimizar todos os empréstimos por padrão
      if (data?.loans) {
        const allLoanIds = new Set(data.loans.map(loan => loan.id))
        setMinimizedLoans(allLoanIds)
      }
    } catch (error) {
      console.error('Erro ao carregar detalhes:', error)
      toast.error('Erro ao carregar detalhes do contato')
    } finally {
      setLoading(false)
    }
  }

  const handleRegisterPayment = (loan, payment) => {
    setSelectedLoan(loan)
    setSelectedPayment(payment)
    setShowPaymentModal(true)
  }

  const handleMarkLoanAsPaid = async (loan) => {
    if (!window.confirm('Tem certeza que deseja marcar este empréstimo como quitado? Todas as parcelas pendentes serão marcadas como pagas.')) {
      return
    }

    try {
      setLoading(true)
      await loanService.markLoanAsCompleted(loan.id)
      toast.success('Empréstimo marcado como quitado!')
      fetchContactDetails()
      onSuccess()
    } catch (error) {
      console.error('Erro ao marcar empréstimo como quitado:', error)
      toast.error('Erro ao marcar empréstimo como quitado')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteLoan = async (loan) => {
    if (!window.confirm(`Tem certeza que deseja deletar o empréstimo "${loan.title}"? Esta ação não pode ser desfeita e todas as transações relacionadas serão removidas.`)) {
      return
    }

    try {
      setLoading(true)
      await loanService.deleteLoanById(loan.id)
      toast.success('Empréstimo deletado com sucesso!')
      fetchContactDetails()
      onSuccess()
    } catch (error) {
      console.error('Erro ao deletar empréstimo:', error)
      toast.error('Erro ao deletar empréstimo')
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentSuccess = async () => {
    // Aguardar um pouco para garantir que o backend processou
    await new Promise(resolve => setTimeout(resolve, 500))
    await fetchContactDetails()
    onSuccess()
  }

  const handleAddPayment = async (loan) => {
    try {
      const info = await loanService.getRemainingInfo(loan.id)
      setRemainingInfo(info)
      setSelectedLoan(loan)
      setShowAddPaymentModal(true)
    } catch (error) {
      toast.error('Erro ao obter informações do empréstimo')
    }
  }

  // Função para alternar minimização de empréstimo
  const toggleLoanMinimized = (loanId) => {
    setMinimizedLoans(prev => {
      const newSet = new Set(prev)
      if (newSet.has(loanId)) {
        newSet.delete(loanId)
      } else {
        newSet.add(loanId)
      }
      return newSet
    })
  }

  // Função para filtrar empréstimos
  const getFilteredLoans = () => {
    if (!contactData?.loans) return []

    console.log('🔍 Filtros aplicados:', loanFilters)
    console.log('📋 Total de empréstimos:', contactData.loans.length)

    const filtered = contactData.loans.filter(loan => {
      // Filtro por busca (nome/título)
      if (loanFilters.search && !loan.title.toLowerCase().includes(loanFilters.search.toLowerCase())) {
        return false
      }

      // Filtro por status
      if (loanFilters.status && loan.status !== loanFilters.status) {
        return false
      }

      // Filtro por tipo
      if (loanFilters.type && loan.type !== loanFilters.type) {
        return false
      }

      // Filtro por data
      if (loanFilters.dateFrom) {
        const loanDate = new Date(loan.startDate)
        const filterDate = new Date(loanFilters.dateFrom)

        // Normalizar datas para comparação (apenas data, sem hora)
        loanDate.setHours(0, 0, 0, 0)
        filterDate.setHours(0, 0, 0, 0)

        if (loanDate < filterDate) return false
      }

      if (loanFilters.dateTo) {
        const loanDate = new Date(loan.startDate)
        const filterDate = new Date(loanFilters.dateTo)

        // Normalizar datas para comparação (apenas data, sem hora)
        loanDate.setHours(0, 0, 0, 0)
        filterDate.setHours(23, 59, 59, 999) // Incluir todo o dia final

        if (loanDate > filterDate) return false
      }

      return true
    })

    console.log('✅ Empréstimos filtrados:', filtered.length)
    return filtered
  }

  // Funções de cálculo de estatísticas
  const calculateOnTimePayments = () => {
    if (!contactData?.loans) return 0

    let onTimeCount = 0
    contactData.loans.forEach(loan => {
      loan.payments?.forEach(payment => {
        if (payment.isPaid && !payment.isLate) {
          onTimeCount++
        }
      })
    })
    return onTimeCount
  }

  const calculateLatePayments = () => {
    if (!contactData?.loans) return 0

    let lateCount = 0
    contactData.loans.forEach(loan => {
      loan.payments?.forEach(payment => {
        if (payment.isPaid && payment.isLate) {
          lateCount++
        }
      })
    })
    return lateCount
  }

  const calculatePunctualityRate = () => {
    const onTime = calculateOnTimePayments()
    const late = calculateLatePayments()
    const total = onTime + late

    if (total === 0) return 100
    return Math.round((onTime / total) * 100)
  }

  // Função para alterar status do contato
  const handleStatusChange = async () => {
    try {
      setLoading(true)
      await contactService.updateContactStatus(contact.id, newStatus)
      toast.success('Status do contato atualizado!')
      setEditingStatus(false)
      fetchContactDetails()
      onSuccess()
    } catch (error) {
      console.error('Erro ao atualizar status:', error)
      toast.error('Erro ao atualizar status do contato')
    } finally {
      setLoading(false)
    }
  }

  // Função para alterar valor da parcela
  const handlePaymentAmountChange = async (loanId, paymentId, newAmount) => {
    try {
      setLoading(true)
      await loanService.updatePaymentAmount(loanId, paymentId, newAmount)
      toast.success('Valor da parcela atualizado!')
      fetchContactDetails()
      onSuccess()
    } catch (error) {
      console.error('Erro ao atualizar parcela:', error)
      toast.error('Erro ao atualizar valor da parcela')
    } finally {
      setLoading(false)
    }
  }

  // Função para deletar parcela
  const handleDeletePayment = async (loanId, paymentId) => {
    if (!window.confirm('Tem certeza que deseja deletar esta parcela?')) {
      return
    }

    try {
      setLoading(true)
      await loanService.deletePayment(loanId, paymentId)
      toast.success('Parcela deletada!')
      fetchContactDetails()
      onSuccess()
    } catch (error) {
      console.error('Erro ao deletar parcela:', error)
      toast.error('Erro ao deletar parcela')
    } finally {
      setLoading(false)
    }
  }

  // Função para alterar data de vencimento
  const handleDueDateChange = async (loanId, paymentId, newDueDate) => {
    try {
      setLoading(true)
      await loanService.updatePaymentDueDate(loanId, paymentId, newDueDate)
      toast.success('Data de vencimento atualizada!')
      fetchContactDetails()
      onSuccess()
    } catch (error) {
      console.error('Erro ao atualizar data:', error)
      toast.error('Erro ao atualizar data de vencimento')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'GOOD':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'BAD':
        return <AlertCircle className="h-5 w-5 text-red-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'GOOD':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'BAD':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLoanStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'OVERDUE':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (!isOpen || !contact) return null

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-center mt-4 text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {contactData?.photoUrl ? (
                <img
                  src={contactData.photoUrl}
                  alt={contactData.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-white"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                  <User className="h-8 w-8" />
                </div>
              )}
              <div>
                <h2 className="text-2xl font-bold">{contactData?.name}</h2>
                <div className="flex items-center gap-2">
                  {editingStatus ? (
                    <div className="flex items-center gap-2">
                      <select
                        value={newStatus}
                        onChange={(e) => setNewStatus(e.target.value)}
                        className="px-2 py-1 text-sm border border-white rounded bg-white bg-opacity-20 text-white"
                      >
                        <option value="GOOD" className="text-gray-900">Bom Pagador</option>
                        <option value="NEUTRAL" className="text-gray-900">Neutro</option>
                        <option value="BAD" className="text-gray-900">Mau Pagador</option>
                      </select>
                      <button
                        onClick={handleStatusChange}
                        className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                      >
                        <Save className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setEditingStatus(false)}
                        className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeColor(contactData?.status)} bg-white`}>
                        {getStatusIcon(contactData?.status)}
                        {loanUtils.getContactStatusLabel(contactData?.status)}
                      </div>
                      <button
                        onClick={() => {
                          setEditingStatus(true)
                          setNewStatus(contactData?.status || 'NEUTRAL')
                        }}
                        className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                        title="Editar status"
                      >
                        <Edit3 className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
          {/* Informações de Contato */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-2">
                <Mail className="h-5 w-5 text-gray-600" />
                <span className="font-medium text-gray-900">Email</span>
              </div>
              <p className="text-gray-700">{contactData?.email || 'Não informado'}</p>
            </div>

            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-2">
                <Phone className="h-5 w-5 text-gray-600" />
                <span className="font-medium text-gray-900">Telefone</span>
              </div>
              <p className="text-gray-700">{contactData?.phone || 'Não informado'}</p>
            </div>

            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-2">
                <DollarSign className="h-5 w-5 text-gray-600" />
                <span className="font-medium text-gray-900">Empréstimos</span>
              </div>
              <p className="text-gray-700">{contactData?.totalLoans || 0} total</p>
            </div>
          </div>

          {/* Estatísticas de Pagamento */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Pagamentos em Dia</p>
                  <p className="text-2xl font-bold text-green-800">{calculateOnTimePayments()}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600">Pagamentos Atrasados</p>
                  <p className="text-2xl font-bold text-red-800">{calculateLatePayments()}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Taxa de Pontualidade</p>
                  <p className="text-2xl font-bold text-blue-800">
                    {calculatePunctualityRate()}%
                  </p>
                </div>
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </div>

          {/* Lista de Empréstimos */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Empréstimos</h3>
              <span className="text-sm text-gray-500">
                {getFilteredLoans().length} de {contactData?.loans?.length || 0} empréstimos
              </span>
            </div>

            {/* Filtros */}
            {contactData?.loans && contactData.loans.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Buscar</label>
                    <input
                      type="text"
                      placeholder="Nome do empréstimo..."
                      value={loanFilters.search}
                      onChange={(e) => setLoanFilters(prev => ({ ...prev, search: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={loanFilters.status}
                      onChange={(e) => setLoanFilters(prev => ({ ...prev, status: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    >
                      <option value="">Todos</option>
                      <option value="ACTIVE">Ativo</option>
                      <option value="COMPLETED">Quitado</option>
                      <option value="OVERDUE">Em Atraso</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
                    <select
                      value={loanFilters.type}
                      onChange={(e) => setLoanFilters(prev => ({ ...prev, type: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    >
                      <option value="">Todos</option>
                      <option value="LOAN_GIVEN">Emprestei</option>
                      <option value="LOAN_RECEIVED">Peguei Emprestado</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Data Início</label>
                    <input
                      type="date"
                      value={loanFilters.dateFrom}
                      onChange={(e) => setLoanFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Data Fim</label>
                    <input
                      type="date"
                      value={loanFilters.dateTo}
                      onChange={(e) => setLoanFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    />
                  </div>

                  <div className="flex items-end">
                    <button
                      onClick={() => setLoanFilters({ search: '', status: '', type: '', dateFrom: '', dateTo: '' })}
                      className="px-4 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      Limpar Filtros
                    </button>
                  </div>
                </div>
              </div>
            )}

            {getFilteredLoans().length > 0 ? (
              <div className="space-y-4">
                {getFilteredLoans().map((loan) => {
                  const isMinimized = minimizedLoans.has(loan.id)

                  return (
                    <div key={loan.id} className="bg-white border border-gray-200 rounded-xl p-6">
                      {/* Header do Empréstimo */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <button
                            onClick={() => toggleLoanMinimized(loan.id)}
                            className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                            title={isMinimized ? "Expandir" : "Minimizar"}
                          >
                            {isMinimized ? (
                              <ChevronRight className="h-5 w-5" />
                            ) : (
                              <ChevronDown className="h-5 w-5" />
                            )}
                          </button>
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900">{loan.title}</h4>
                            <div className="flex items-center gap-4 mt-1">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLoanStatusColor(loan.status)}`}>
                                {loanUtils.getStatusLabel(loan.status)}
                              </span>
                              <span className="text-sm text-gray-600">
                                {loanUtils.getTypeLabel(loan.type)}
                              </span>
                              <span className="text-sm text-gray-600">
                                {loanUtils.getLoanTypeLabel(loan.loanType)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-gray-900">
                            {loanUtils.formatCurrency(loan.totalAmount)}
                          </p>
                          <p className="text-sm text-gray-600">
                            {loan.paidInstallments}/{loan.installments} parcelas
                            {loan.payments && loan.payments.length > loan.installments && (
                              <span className="text-blue-600 font-medium ml-1">
                                (+{loan.payments.length - loan.installments} adicionadas)
                              </span>
                            )}
                          </p>
                          <div className="flex gap-2 mt-2 flex-wrap">
                            {loan.status === 'ACTIVE' && (
                              <>
                                <button
                                  onClick={() => handleMarkLoanAsPaid(loan)}
                                  className="px-3 py-1 text-xs bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                >
                                  Marcar como Quitado
                                </button>
                                <button
                                  onClick={() => handleAddPayment(loan)}
                                  className="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-1"
                                >
                                  <Plus className="h-3 w-3" />
                                  Adicionar Parcela
                                </button>
                              </>
                            )}
                            <button
                              onClick={() => handleDeleteLoan(loan)}
                              className="px-3 py-1 text-xs bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-1"
                            >
                              <Trash2 className="h-3 w-3" />
                              Deletar
                            </button>
                          </div>
                        </div>
                      </div>

                    {/* Progresso */}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Progresso (por valor)</span>
                        <span>{loanUtils.calculateValueProgress(loan)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${loanUtils.calculateValueProgress(loan)}%` }}
                        />
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>Pago: {loanUtils.formatCurrency(loan.payments?.filter(p => p.isPaid).reduce((sum, p) => sum + p.amount, 0) || 0)}</span>
                        <span>Total: {loanUtils.formatCurrency(loan.totalAmount)}</span>
                      </div>
                    </div>

                    {/* Conteúdo colapsável */}
                    {!isMinimized && (
                      <div>
                        {/* Informações Adicionais do Empréstimo */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <h5 className="font-medium text-gray-900 mb-3">Detalhes do Empréstimo</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Data de Início</p>
                          <p className="font-medium">{loanUtils.formatDate(loan.startDate)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Valor da Parcela</p>
                          <p className="font-medium">{loanUtils.formatCurrency(loan.installmentAmount)}</p>
                        </div>
                        {loan.bank && (
                          <div>
                            <p className="text-sm text-gray-600">Banco</p>
                            <p className="font-medium">{loan.bank.icon} {loan.bank.name}</p>
                          </div>
                        )}
                        <div>
                          <p className="text-sm text-gray-600">Criado em</p>
                          <p className="font-medium">{loanUtils.formatDate(loan.createdAt)}</p>
                        </div>
                      </div>

                      {loan.description && (
                        <div className="mt-4">
                          <p className="text-sm text-gray-600">Descrição</p>
                          <p className="text-sm text-gray-900 bg-white p-3 rounded border">{loan.description}</p>
                        </div>
                      )}

                      {loan.receiptUrl && (
                        <div className="mt-4">
                          <p className="text-sm text-gray-600 mb-2">Comprovante</p>
                          <div className="flex items-center gap-2">
                            <img
                              src={loan.receiptUrl}
                              alt="Comprovante do empréstimo"
                              className="w-16 h-16 object-cover rounded border cursor-pointer hover:opacity-80"
                              onClick={() => window.open(loan.receiptUrl, '_blank')}
                            />
                            <button
                              onClick={() => window.open(loan.receiptUrl, '_blank')}
                              className="flex items-center gap-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                            >
                              <Eye className="h-3 w-3" />
                              Ver Comprovante
                            </button>
                          </div>
                        </div>
                      )}

                      {loan.notes && (
                        <div className="mt-4">
                          <p className="text-sm text-gray-600">Observações</p>
                          <p className="text-sm text-gray-900 bg-white p-3 rounded border">{loan.notes}</p>
                        </div>
                      )}
                    </div>

                    {/* Cronograma de Parcelas */}
                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <h5 className="font-medium text-gray-900">Cronograma de Parcelas</h5>
                        <button
                          onClick={() => setShowPaymentTable(prev => ({
                            ...prev,
                            [loan.id]: !prev[loan.id]
                          }))}
                          className="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          {showPaymentTable[loan.id] ? 'Vista Simples' : 'Vista Detalhada'}
                        </button>
                      </div>

                      {showPaymentTable[loan.id] ? (
                        /* Tabela Detalhada */
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="bg-gray-50">
                                <th className="px-3 py-2 text-left">#</th>
                                <th className="px-3 py-2 text-left">Data Prevista</th>
                                <th className="px-3 py-2 text-left">Data Pagamento</th>
                                <th className="px-3 py-2 text-left">Valor</th>
                                <th className="px-3 py-2 text-left">Status</th>
                                <th className="px-3 py-2 text-left">Ações</th>
                              </tr>
                            </thead>
                            <tbody>
                              {loan.payments?.map((payment, index) => {
                                const isOverdue = loanUtils.isOverdue(payment.dueDate, payment.isPaid)
                                const isEditing = editingPayments[payment.id]

                                return (
                                  <tr key={payment.id} className={`border-b ${
                                    payment.isPaid ? 'bg-green-50' : isOverdue ? 'bg-red-50' : 'bg-white'
                                  }`}>
                                    <td className="px-3 py-2 font-medium">{index + 1}</td>
                                    <td className="px-3 py-2">
                                      {isEditing ? (
                                        <input
                                          type="date"
                                          defaultValue={payment.dueDate?.split('T')[0]}
                                          onBlur={(e) => handleDueDateChange(loan.id, payment.id, e.target.value)}
                                          className="w-full px-2 py-1 text-xs border rounded"
                                        />
                                      ) : (
                                        loanUtils.formatDate(payment.dueDate)
                                      )}
                                    </td>
                                    <td className="px-3 py-2">
                                      {payment.isPaid ? loanUtils.formatDate(payment.paymentDate) : '-'}
                                    </td>
                                    <td className="px-3 py-2">
                                      {isEditing ? (
                                        <input
                                          type="number"
                                          step="0.01"
                                          defaultValue={payment.amount}
                                          onBlur={(e) => handlePaymentAmountChange(loan.id, payment.id, e.target.value)}
                                          className="w-full px-2 py-1 text-xs border rounded"
                                        />
                                      ) : (
                                        loanUtils.formatCurrency(payment.amount)
                                      )}
                                    </td>
                                    <td className="px-3 py-2">
                                      <span className={`px-2 py-1 text-xs rounded-full ${
                                        payment.isPaid
                                          ? 'bg-green-100 text-green-800'
                                          : isOverdue
                                          ? 'bg-red-100 text-red-800'
                                          : 'bg-gray-100 text-gray-800'
                                      }`}>
                                        {payment.isPaid ? 'Pago' : isOverdue ? 'Atrasado' : 'Pendente'}
                                      </span>
                                    </td>
                                    <td className="px-3 py-2">
                                      <div className="flex gap-1">
                                        {!payment.isPaid && (
                                          <button
                                            onClick={() => handleRegisterPayment(loan, payment)}
                                            className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                                          >
                                            Pagar
                                          </button>
                                        )}
                                        <button
                                          onClick={() => setEditingPayments(prev => ({
                                            ...prev,
                                            [payment.id]: !prev[payment.id]
                                          }))}
                                          className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
                                        >
                                          {isEditing ? 'Salvar' : 'Editar'}
                                        </button>
                                        <button
                                          onClick={() => handleDeletePayment(loan.id, payment.id)}
                                          className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </button>
                                      </div>
                                    </td>
                                  </tr>
                                )
                              })}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        /* Vista Simples Original */
                        <div className="space-y-2 max-h-40 overflow-y-auto">
                        {loan.payments?.map((payment, index) => {
                          const isOverdue = loanUtils.isOverdue(payment.dueDate, payment.isPaid)

                          return (
                            <div
                              key={payment.id}
                              className={`flex items-center justify-between p-3 rounded-lg border ${
                                payment.isPaid
                                  ? 'bg-green-50 border-green-200'
                                  : isOverdue
                                  ? 'bg-red-50 border-red-200'
                                  : 'bg-gray-50 border-gray-200'
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${
                                  payment.isPaid
                                    ? 'bg-green-500 text-white'
                                    : isOverdue
                                    ? 'bg-red-500 text-white'
                                    : 'bg-gray-300 text-gray-600'
                                }`}>
                                  {payment.isPaid ? '✓' : index + 1}
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">
                                    Parcela {index + 1}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    Vencimento: {loanUtils.formatDate(payment.dueDate)}
                                  </p>
                                </div>
                              </div>

                              <div className="flex items-center gap-3">
                                <div className="text-right">
                                  <p className="font-semibold text-gray-900">
                                    {loanUtils.formatCurrency(payment.amount)}
                                  </p>
                                  <p className={`text-xs font-medium ${
                                    payment.isPaid
                                      ? 'text-green-600'
                                      : isOverdue
                                      ? 'text-red-600'
                                      : 'text-gray-500'
                                  }`}>
                                    {payment.isPaid
                                      ? `Pago em ${loanUtils.formatDate(payment.paymentDate)}`
                                      : isOverdue
                                      ? 'Em atraso'
                                      : 'Pendente'
                                    }
                                  </p>
                                </div>

                                {!payment.isPaid && (
                                  <button
                                    onClick={() => handleRegisterPayment(loan, payment)}
                                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                  >
                                    Pagar
                                  </button>
                                )}

                                {payment.receiptUrl && (
                                  <button
                                    onClick={() => window.open(payment.receiptUrl, '_blank')}
                                    className="p-1 text-gray-600 hover:text-blue-600 transition-colors"
                                    title="Ver comprovante"
                                  >
                                    <Eye className="h-4 w-4" />
                                  </button>
                                )}
                              </div>
                            </div>
                          )
                        })}
                        </div>
                      )}
                    </div>
                      </div>
                    )}
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <DollarSign className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum empréstimo encontrado
                </h3>
                <p className="text-gray-600">
                  Este contato ainda não possui empréstimos registrados.
                </p>
              </div>
            )}
          </div>

          {/* Notas */}
          {contactData?.notes && (
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
              <h4 className="font-medium text-yellow-800 mb-2">Observações</h4>
              <p className="text-yellow-700 text-sm">{contactData.notes}</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>

      {/* Modal de Pagamento */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        loan={selectedLoan}
        payment={selectedPayment}
        banks={banks}
        onSuccess={handlePaymentSuccess}
      />

      {/* Modal de Adicionar Parcela */}
      <AddPaymentModal
        isOpen={showAddPaymentModal}
        onClose={() => setShowAddPaymentModal(false)}
        loan={selectedLoan}
        remainingInfo={remainingInfo}
        onSuccess={handlePaymentSuccess}
      />
    </div>
  )
}

export default ContactDetailModal
