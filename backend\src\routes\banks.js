const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar bancos do usuário
router.get('/', async (req, res) => {
  try {
    const banks = await prisma.bank.findMany({
      where: { userId: req.user.id },
      orderBy: { name: 'asc' }
    });

    res.json(banks);
  } catch (error) {
    console.error('Erro ao buscar bancos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar banco
router.post('/', async (req, res) => {
  try {
    const {
      name,
      icon,
      color,
      initialBalance,
      billDueDay,
      creditLimit,
      isLimitLocked
    } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }

    const bank = await prisma.bank.create({
      data: {
        name,
        icon: icon || '🏦',
        color: color || '#3B82F6',
        initialBalance: initialBalance || 0,
        currentBalance: initialBalance || 0,
        billDueDay: billDueDay || null,
        creditLimit: creditLimit ? parseFloat(creditLimit) : 0,
        availableLimit: creditLimit ? parseFloat(creditLimit) : 0,
        isLimitLocked: isLimitLocked || false,
        userId: req.user.id
      }
    });

    res.status(201).json(bank);
  } catch (error) {
    console.error('Erro ao criar banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar banco
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      icon,
      color,
      isVisible,
      billDueDay,
      creditLimit,
      availableLimit,
      isLimitLocked
    } = req.body;

    const bank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const updatedBank = await prisma.bank.update({
      where: { id },
      data: {
        name: name || bank.name,
        icon: icon || bank.icon,
        color: color || bank.color,
        isVisible: isVisible !== undefined ? isVisible : bank.isVisible,
        billDueDay: billDueDay !== undefined ? billDueDay : bank.billDueDay,
        creditLimit: creditLimit !== undefined ? parseFloat(creditLimit) : bank.creditLimit,
        availableLimit: availableLimit !== undefined ? parseFloat(availableLimit) : bank.availableLimit,
        isLimitLocked: isLimitLocked !== undefined ? isLimitLocked : bank.isLimitLocked
      }
    });

    res.json(updatedBank);
  } catch (error) {
    console.error('Erro ao atualizar banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar banco
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Verificar se há transações vinculadas
    const transactionCount = await prisma.transaction.count({
      where: { bankId: id }
    });

    if (transactionCount > 0) {
      return res.status(400).json({
        error: 'Não é possível excluir banco com transações vinculadas'
      });
    }

    await prisma.bank.delete({
      where: { id }
    });

    res.json({ message: 'Banco excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter saldo total
router.get('/balance', async (req, res) => {
  try {
    const banks = await prisma.bank.findMany({
      where: {
        userId: req.user.id,
        isVisible: true
      }
    });

    const totalBalance = banks.reduce((sum, bank) => sum + bank.currentBalance, 0);
    const bankCount = banks.length;

    res.json({
      totalBalance,
      bankCount,
      banks: banks.map(bank => ({
        id: bank.id,
        name: bank.name,
        icon: bank.icon,
        color: bank.color,
        currentBalance: bank.currentBalance
      }))
    });
  } catch (error) {
    console.error('Erro ao buscar saldo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Transferir entre bancos
router.post('/transfer', async (req, res) => {
  try {
    const { fromBankId, toBankId, amount, description } = req.body;

    if (!fromBankId || !toBankId || !amount || amount <= 0) {
      return res.status(400).json({ error: 'Dados inválidos para transferência' });
    }

    if (fromBankId === toBankId) {
      return res.status(400).json({ error: 'Banco de origem e destino devem ser diferentes' });
    }

    // Verificar se os bancos existem e pertencem ao usuário
    const [fromBank, toBank] = await Promise.all([
      prisma.bank.findFirst({ where: { id: fromBankId, userId: req.user.id } }),
      prisma.bank.findFirst({ where: { id: toBankId, userId: req.user.id } })
    ]);

    if (!fromBank || !toBank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    if (fromBank.currentBalance < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente' });
    }

    // Realizar transferência em transação
    await prisma.$transaction(async (tx) => {
      // Debitar do banco origem
      await tx.bank.update({
        where: { id: fromBankId },
        data: { currentBalance: fromBank.currentBalance - amount }
      });

      // Creditar no banco destino
      await tx.bank.update({
        where: { id: toBankId },
        data: { currentBalance: toBank.currentBalance + amount }
      });

      // Criar transação de saída
      await tx.transaction.create({
        data: {
          description: description || `Transferência para ${toBank.name}`,
          amount,
          type: 'EXPENSE',
          userId: req.user.id,
          bankId: fromBankId
        }
      });

      // Criar transação de entrada
      await tx.transaction.create({
        data: {
          description: description || `Transferência de ${fromBank.name}`,
          amount,
          type: 'INCOME',
          userId: req.user.id,
          bankId: toBankId
        }
      });
    });

    res.json({ message: 'Transferência realizada com sucesso' });
  } catch (error) {
    console.error('Erro ao realizar transferência:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Pagar fatura do banco
router.post('/:id/pay-bill', async (req, res) => {
  try {
    const { id } = req.params;
    const { paymentBankId, paymentMethodId } = req.body; // Banco/método para pagar a fatura

    // Verificar se o banco da fatura existe e pertence ao usuário
    const billBank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!billBank) {
      return res.status(404).json({ error: 'Banco da fatura não encontrado' });
    }

    // Verificar se o banco de pagamento foi informado e existe
    if (!paymentBankId) {
      return res.status(400).json({ error: 'Banco para pagamento é obrigatório' });
    }

    const paymentBank = await prisma.bank.findFirst({
      where: { id: paymentBankId, userId: req.user.id }
    });

    if (!paymentBank) {
      return res.status(404).json({ error: 'Banco para pagamento não encontrado' });
    }

    // Buscar todas as formas de pagamento de crédito do banco da fatura
    const creditMethods = await prisma.paymentMethod.findMany({
      where: {
        bankId: id,
        type: 'CREDIT',
        userId: req.user.id
      }
    });

    if (creditMethods.length === 0) {
      return res.status(400).json({ error: 'Nenhum cartão de crédito encontrado para este banco' });
    }

    // Calcular total da fatura primeiro para validar saldo
    let totalBill = 0;

    const currentDate = new Date();
    const currentMonthNumber = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Buscar todas as transações do mês atual com cartão de crédito deste banco que ainda não foram pagas
    const unpaidTransactions = await prisma.transaction.findMany({
      where: {
        bankId: id,
        paymentMethod: {
          type: 'CREDIT'
        },
        date: {
          gte: new Date(currentYear, currentMonthNumber, 1),
          lt: new Date(currentYear, currentMonthNumber + 1, 1)
        },
        isPaid: false,
        userId: req.user.id
      },
      include: {
        paymentMethod: true
      }
    });

    // Buscar TODAS as assinaturas ativas deste banco para gerar transações de débito
    const currentMonth = currentDate.getFullYear() + '-' + String(currentDate.getMonth() + 1).padStart(2, '0');

    // Buscar assinaturas que foram cobradas este mês mas ainda não pagas
    const billedSubscriptions = await prisma.subscription.findMany({
      where: {
        paymentMethod: {
          bankId: id,
          type: 'CREDIT'
        },
        isActive: true,
        lastBilledMonth: currentMonth,
        isPaidThisMonth: false,
        userId: req.user.id
      },
      include: {
        paymentMethod: true,
        category: true
      }
    });

    // Buscar TODAS as assinaturas ativas do banco para criar transações de débito
    const allActiveSubscriptions = await prisma.subscription.findMany({
      where: {
        paymentMethod: {
          bankId: id,
          type: 'CREDIT'
        },
        isActive: true,
        userId: req.user.id
      },
      include: {
        paymentMethod: true,
        category: true
      }
    });

    // Calcular total da fatura (apenas transações não pagas + assinaturas cobradas este mês)
    totalBill = unpaidTransactions.reduce((sum, t) => sum + t.amount, 0) +
                billedSubscriptions.reduce((sum, s) => sum + s.amount, 0);

    console.log(`=== PAGAMENTO DE FATURA ===`);
    console.log(`Banco da fatura: ${billBank.name}`);
    console.log(`Banco de pagamento: ${paymentBank.name} (Saldo: ${paymentBank.currentBalance})`);
    console.log(`Total da fatura: ${totalBill}`);
    console.log(`Transações não pagas: ${unpaidTransactions.length}`);
    console.log(`Assinaturas cobradas: ${billedSubscriptions.length}`);
    console.log(`Todas as assinaturas ativas: ${allActiveSubscriptions.length}`);

    // Verificar se o banco de pagamento tem saldo suficiente
    if (paymentBank.currentBalance < totalBill) {
      return res.status(400).json({
        error: 'Saldo insuficiente no banco para pagamento',
        required: totalBill,
        available: paymentBank.currentBalance
      });
    }

    let transactionsUpdated = 0;

    // Usar transação do Prisma para garantir consistência
    await prisma.$transaction(async (tx) => {
      // Marcar todas as transações como pagas
      for (const transaction of unpaidTransactions) {
        await tx.transaction.update({
          where: { id: transaction.id },
          data: { isPaid: true }
        });
        transactionsUpdated++;
      }

      // Criar transações de débito para TODAS as assinaturas ativas do cartão
      for (const subscription of allActiveSubscriptions) {
        await tx.transaction.create({
          data: {
            description: `Pagamento - ${subscription.name}`,
            amount: subscription.amount,
            type: 'EXPENSE',
            date: new Date(),
            bankId: id,
            paymentMethodId: subscription.paymentMethodId,
            categoryId: subscription.categoryId,
            isPaid: true, // Marcar como pago imediatamente
            userId: req.user.id
          }
        });
      }

      // Marcar TODAS as assinaturas ativas como pagas (pois pagou a fatura)
      for (const subscription of allActiveSubscriptions) {
        await tx.subscription.update({
          where: { id: subscription.id },
          data: {
            isPaidThisMonth: true
          }
        });
      }

      // Criar transação de pagamento da fatura no banco de pagamento
      if (totalBill > 0) {
        await tx.transaction.create({
          data: {
            description: `Pagamento de Fatura - ${billBank.name}`,
            amount: totalBill,
            type: 'EXPENSE',
            date: new Date(),
            bankId: paymentBankId,
            paymentMethodId: paymentMethodId || null,
            isPaid: true,
            userId: req.user.id
          }
        });
      }

      // Zerar faturas de todas as formas de pagamento de crédito do banco da fatura
      for (const method of creditMethods) {
        await tx.paymentMethod.update({
          where: { id: method.id },
          data: {
            currentBill: 0,
            isBillPaid: true
          }
        });
      }

      // Resetar limite disponível do banco da fatura
      await tx.bank.update({
        where: { id },
        data: {
          availableLimit: billBank.creditLimit
        }
      });

      // Debitar o valor da fatura do banco de pagamento
      const updatedPaymentBank = await tx.bank.update({
        where: { id: paymentBankId },
        data: {
          currentBalance: {
            decrement: totalBill
          }
        }
      });

      console.log(`Debitando ${totalBill} do banco ${paymentBank.name}. Saldo anterior: ${paymentBank.currentBalance}, Novo saldo: ${updatedPaymentBank.currentBalance}`);
    });

    // Buscar o banco de pagamento atualizado para mostrar o saldo correto
    const finalPaymentBank = await prisma.bank.findUnique({
      where: { id: paymentBankId }
    });

    res.json({
      message: 'Fatura paga com sucesso',
      totalPaid: totalBill,
      subscriptionTransactionsCreated: allActiveSubscriptions.length, // Todas as assinaturas
      subscriptionsMarkedAsPaid: allActiveSubscriptions.length, // Todas marcadas como pagas
      transactionsUpdated: transactionsUpdated,
      billBank: billBank.name,
      paymentBank: paymentBank.name,
      remainingBalance: finalPaymentBank.currentBalance
    });
  } catch (error) {
    console.error('Erro ao pagar fatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar bancos disponíveis para pagamento de fatura
router.get('/payment-options', async (req, res) => {
  try {
    // Buscar todos os bancos do usuário com saldo > 0
    const banks = await prisma.bank.findMany({
      where: {
        userId: req.user.id,
        currentBalance: {
          gt: 0
        }
      },
      include: {
        paymentMethods: {
          where: {
            type: {
              in: ['DEBIT', 'PIX', 'CASH']
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.json(banks);
  } catch (error) {
    console.error('Erro ao buscar opções de pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar histórico de faturas pagas
router.get('/bill-history', async (req, res) => {
  try {
    // Buscar transações de pagamento de fatura
    const billPayments = await prisma.transaction.findMany({
      where: {
        userId: req.user.id,
        description: {
          startsWith: 'Pagamento de Fatura'
        },
        isPaid: true
      },
      include: {
        bank: true,
        paymentMethod: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Para cada pagamento de fatura, buscar as transações relacionadas
    const billHistory = await Promise.all(
      billPayments.map(async (payment) => {
        const billBankName = payment.description.replace('Pagamento de Fatura - ', '');

        // Buscar o banco da fatura
        const billBank = await prisma.bank.findFirst({
          where: {
            name: billBankName,
            userId: req.user.id
          }
        });

        if (!billBank) return null;

        // Buscar transações do mesmo dia que foram pagas junto com esta fatura
        const startOfDay = new Date(payment.date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(payment.date);
        endOfDay.setHours(23, 59, 59, 999);

        const relatedTransactions = await prisma.transaction.findMany({
          where: {
            userId: req.user.id,
            bankId: billBank.id,
            date: {
              gte: startOfDay,
              lte: endOfDay
            },
            description: {
              startsWith: 'Pagamento - '
            },
            isPaid: true
          },
          include: {
            category: true,
            paymentMethod: true
          }
        });

        return {
          id: payment.id,
          date: payment.date,
          totalAmount: payment.amount,
          billBank: billBank,
          paymentBank: payment.bank,
          paymentMethod: payment.paymentMethod,
          creditLimit: billBank.creditLimit,
          transactions: relatedTransactions
        };
      })
    );

    // Filtrar resultados nulos
    const validHistory = billHistory.filter(item => item !== null);

    res.json(validHistory);
  } catch (error) {
    console.error('Erro ao buscar histórico de faturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
