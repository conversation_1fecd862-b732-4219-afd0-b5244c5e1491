/**
 * @swagger
 * components:
 *   schemas:
 *     Bank:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           description: ID único do banco
 *         name:
 *           type: string
 *           description: Nome do banco
 *         icon:
 *           type: string
 *           description: Ícone do banco
 *         color:
 *           type: string
 *           description: Cor do banco
 *         balance:
 *           type: number
 *           description: Saldo atual do banco
 *         creditLimit:
 *           type: number
 *           description: Limite de crédito
 */

const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

/**
 * @swagger
 * /banks:
 *   get:
 *     summary: Lista todos os bancos do usuário
 *     tags: [Banks]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de bancos retornada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Bank'
 *       401:
 *         description: Token de autenticação inválido
 *       500:
 *         description: Erro interno do servidor
 */
// Listar bancos do usuário
router.get('/', async (req, res) => {
  try {
    const banks = await prisma.bank.findMany({
      where: { userId: req.user.id },
      orderBy: { name: 'asc' }
    });

    res.json(banks);
  } catch (error) {
    console.error('Erro ao buscar bancos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar banco
router.post('/', async (req, res) => {
  try {
    const {
      name,
      icon,
      color,
      initialBalance,
      billDueDay,
      creditLimit,
      isLimitLocked
    } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }

    const bank = await prisma.bank.create({
      data: {
        name,
        icon: icon || '🏦',
        color: color || '#3B82F6',
        initialBalance: initialBalance || 0,
        currentBalance: initialBalance || 0,
        billDueDay: billDueDay || null,
        creditLimit: creditLimit ? parseFloat(creditLimit) : 0,
        availableLimit: creditLimit ? parseFloat(creditLimit) : 0,
        isLimitLocked: isLimitLocked || false,
        userId: req.user.id
      }
    });

    res.status(201).json(bank);
  } catch (error) {
    console.error('Erro ao criar banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar banco
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      icon,
      color,
      isVisible,
      billDueDay,
      creditLimit,
      availableLimit,
      isLimitLocked
    } = req.body;

    const bank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const updatedBank = await prisma.bank.update({
      where: { id },
      data: {
        name: name || bank.name,
        icon: icon || bank.icon,
        color: color || bank.color,
        isVisible: isVisible !== undefined ? isVisible : bank.isVisible,
        billDueDay: billDueDay !== undefined ? billDueDay : bank.billDueDay,
        creditLimit: creditLimit !== undefined ? parseFloat(creditLimit) : bank.creditLimit,
        availableLimit: availableLimit !== undefined ? parseFloat(availableLimit) : bank.availableLimit,
        isLimitLocked: isLimitLocked !== undefined ? isLimitLocked : bank.isLimitLocked
      }
    });

    res.json(updatedBank);
  } catch (error) {
    console.error('Erro ao atualizar banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar banco
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Verificar se há transações vinculadas
    const transactionCount = await prisma.transaction.count({
      where: { bankId: id }
    });

    if (transactionCount > 0) {
      return res.status(400).json({
        error: 'Não é possível excluir banco com transações vinculadas'
      });
    }

    await prisma.bank.delete({
      where: { id }
    });

    res.json({ message: 'Banco excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter saldo total
router.get('/balance', async (req, res) => {
  try {
    const banks = await prisma.bank.findMany({
      where: {
        userId: req.user.id,
        isVisible: true
      }
    });

    const totalBalance = banks.reduce((sum, bank) => sum + bank.currentBalance, 0);
    const bankCount = banks.length;

    res.json({
      totalBalance,
      bankCount,
      banks: banks.map(bank => ({
        id: bank.id,
        name: bank.name,
        icon: bank.icon,
        color: bank.color,
        currentBalance: bank.currentBalance
      }))
    });
  } catch (error) {
    console.error('Erro ao buscar saldo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Transferir entre bancos
router.post('/transfer', async (req, res) => {
  try {
    const { fromBankId, toBankId, amount, description } = req.body;

    if (!fromBankId || !toBankId || !amount || amount <= 0) {
      return res.status(400).json({ error: 'Dados inválidos para transferência' });
    }

    if (fromBankId === toBankId) {
      return res.status(400).json({ error: 'Banco de origem e destino devem ser diferentes' });
    }

    // Verificar se os bancos existem e pertencem ao usuário
    const [fromBank, toBank] = await Promise.all([
      prisma.bank.findFirst({ where: { id: fromBankId, userId: req.user.id } }),
      prisma.bank.findFirst({ where: { id: toBankId, userId: req.user.id } })
    ]);

    if (!fromBank || !toBank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    if (fromBank.currentBalance < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente' });
    }

    // Realizar transferência em transação
    await prisma.$transaction(async (tx) => {
      // Debitar do banco origem
      await tx.bank.update({
        where: { id: fromBankId },
        data: { currentBalance: fromBank.currentBalance - amount }
      });

      // Creditar no banco destino
      await tx.bank.update({
        where: { id: toBankId },
        data: { currentBalance: toBank.currentBalance + amount }
      });

      // Criar transação de saída
      await tx.transaction.create({
        data: {
          description: description || `Transferência para ${toBank.name}`,
          amount,
          type: 'EXPENSE',
          userId: req.user.id,
          bankId: fromBankId
        }
      });

      // Criar transação de entrada
      await tx.transaction.create({
        data: {
          description: description || `Transferência de ${fromBank.name}`,
          amount,
          type: 'INCOME',
          userId: req.user.id,
          bankId: toBankId
        }
      });
    });

    res.json({ message: 'Transferência realizada com sucesso' });
  } catch (error) {
    console.error('Erro ao realizar transferência:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Pagar fatura do banco
router.post('/:id/pay-bill', async (req, res) => {
  console.log('='.repeat(50));
  console.log('🚨 FUNÇÃO DE PAGAMENTO EXECUTADA!');
  console.log('='.repeat(50));
  console.log('Timestamp:', new Date().toISOString());
  console.log('Dados recebidos:', req.body);
  console.log('ID do banco:', req.params.id);
  console.log('User ID:', req.user?.id);

  try {
    const { id } = req.params;
    const { paymentBankId } = req.body;

    console.log('🔥 PAGAMENTO DE FATURA INICIADO');
    console.log('Banco da fatura:', id);
    console.log('Banco de pagamento:', paymentBankId);

    // Buscar banco da fatura
    const billBank = await prisma.bank.findUnique({
      where: { id, userId: req.user.id }
    });

    if (!billBank) {
      console.log('❌ Banco da fatura não encontrado');
      return res.status(404).json({ error: 'Banco da fatura não encontrado' });
    }

    console.log('✅ Banco da fatura encontrado:', billBank.name);

    // Buscar banco de pagamento
    const paymentBank = await prisma.bank.findUnique({
      where: { id: paymentBankId, userId: req.user.id }
    });

    if (!paymentBank) {
      console.log('❌ Banco de pagamento não encontrado');
      return res.status(404).json({ error: 'Banco de pagamento não encontrado' });
    }

    console.log('✅ Banco de pagamento encontrado:', paymentBank.name);
    console.log('💰 Saldo atual do banco de pagamento:', paymentBank.currentBalance);

    // Buscar todas as assinaturas ativas do banco
    const activeSubscriptions = await prisma.subscription.findMany({
      where: {
        paymentMethod: {
          bankId: id,
          type: 'CREDIT'
        },
        isActive: true,
        userId: req.user.id,
        isPaidThisMonth: false
      },
      include: {
        paymentMethod: true,
        category: true
      }
    });

    console.log('📋 Assinaturas ativas encontradas:', activeSubscriptions.length);

    // Calcular total da fatura CORRETAMENTE:
    // 1. Assinaturas ativas do banco (não pagas este mês)
    const unpaidSubscriptions = await prisma.subscription.findMany({
      where: {
        paymentMethod: {
          bankId: id,
          type: 'CREDIT'
        },
        isActive: true,
        isPaidThisMonth: false,
        userId: req.user.id
      }
    });

    // 2. Transações não pagas com cartão de crédito deste banco
    const unpaidCreditTransactions = await prisma.transaction.findMany({
      where: {
        paymentMethod: {
          bankId: id,
          type: 'CREDIT'
        },
        isPaid: false,
        userId: req.user.id
      }
    });

    // Calcular total da fatura
    const subscriptionsTotal = unpaidSubscriptions.reduce((sum, sub) => sum + sub.amount, 0);
    const transactionsTotal = unpaidCreditTransactions.reduce((sum, trans) => sum + trans.amount, 0);
    const totalBill = subscriptionsTotal + transactionsTotal;

    console.log('💰 Assinaturas não pagas:', subscriptionsTotal);
    console.log('💰 Transações não pagas:', transactionsTotal);

    console.log('💰 Total da fatura:', totalBill);
    console.log('💳 Saldo disponível:', paymentBank.currentBalance);

    // Verificar se há fatura para pagar
    if (totalBill <= 0) {
      console.log('❌ Não há fatura para pagar');
      return res.status(400).json({
        error: 'Não há fatura para pagar neste banco',
        totalBill: totalBill,
        subscriptionsTotal: subscriptionsTotal,
        transactionsTotal: transactionsTotal
      });
    }

    // Verificar se o banco de pagamento tem saldo suficiente
    if (paymentBank.currentBalance < totalBill) {
      console.log('❌ Saldo insuficiente');
      return res.status(400).json({
        error: 'Saldo insuficiente no banco para pagamento',
        required: totalBill,
        available: paymentBank.currentBalance
      });
    }

    console.log('🔄 Iniciando transação de pagamento...');

    // Executar pagamento em transação
    await prisma.$transaction(async (tx) => {
      console.log('📝 Criando transações para assinaturas não pagas...');

      // 1. Criar transações de débito para assinaturas não pagas este mês
      for (const subscription of unpaidSubscriptions) {
        await tx.transaction.create({
          data: {
            description: `Pagamento - ${subscription.name}`,
            amount: subscription.amount,
            type: 'EXPENSE',
            date: new Date(),
            bankId: id,
            paymentMethodId: subscription.paymentMethodId,
            categoryId: subscription.categoryId,
            isPaid: true,
            userId: req.user.id
          }
        });
        console.log(`✅ Transação criada para assinatura: ${subscription.name}`);
      }

      console.log('🔄 Marcando assinaturas como pagas...');

      // 2. Marcar assinaturas não pagas como pagas
      for (const subscription of unpaidSubscriptions) {
        await tx.subscription.update({
          where: { id: subscription.id },
          data: { isPaidThisMonth: true }
        });
        console.log(`✅ Assinatura marcada como paga: ${subscription.name}`);
      }

      console.log('💳 Marcando transações de crédito como pagas...');

      // 3. Marcar transações não pagas de cartão de crédito como pagas
      for (const transaction of unpaidCreditTransactions) {
        await tx.transaction.update({
          where: { id: transaction.id },
          data: { isPaid: true }
        });
        console.log(`✅ Transação marcada como paga: ${transaction.description}`);
      }

      console.log('💸 Criando transação de pagamento da fatura...');

      // 4. Criar transação de pagamento da fatura
      if (totalBill > 0) {
        await tx.transaction.create({
          data: {
            description: `Pagamento de Fatura - ${billBank.name}`,
            amount: totalBill,
            type: 'EXPENSE',
            date: new Date(),
            bankId: paymentBankId,
            isPaid: true,
            userId: req.user.id
          }
        });
        console.log('✅ Transação de pagamento da fatura criada');
      }

      console.log('💰 DEBITANDO VALOR DO BANCO DE PAGAMENTO...');
      console.log('Valor a ser debitado:', totalBill);
      console.log('Saldo antes:', paymentBank.currentBalance);

      // 5. DEBITAR VALOR DO BANCO DE PAGAMENTO - ESTA É A PARTE CRÍTICA!
      const updatedPaymentBank = await tx.bank.update({
        where: { id: paymentBankId },
        data: {
          currentBalance: {
            decrement: totalBill
          }
        }
      });

      console.log('✅ Valor debitado com sucesso!');
      console.log('Saldo depois:', updatedPaymentBank.currentBalance);

      console.log('🔄 Restaurando limite disponível...');

      // 6. Restaurar limite disponível do banco (incrementar pelo valor pago)
      await tx.bank.update({
        where: { id },
        data: {
          availableLimit: {
            increment: totalBill
          }
        }
      });
      console.log('✅ Limite restaurado, incrementado em:', totalBill);
    });

    console.log('🎉 Pagamento concluído com sucesso!');

    // Buscar saldo atualizado
    const updatedPaymentBank = await prisma.bank.findUnique({
      where: { id: paymentBankId }
    });

    const response = {
      message: 'Fatura paga com sucesso',
      totalPaid: totalBill,
      subscriptionsMarkedAsPaid: unpaidSubscriptions.length,
      transactionsMarkedAsPaid: unpaidCreditTransactions.length,
      billBank: billBank.name,
      paymentBank: paymentBank.name,
      remainingBalance: updatedPaymentBank.currentBalance
    };

    console.log('📤 Enviando resposta:', response);

    res.json(response);

  } catch (error) {
    console.error('🚨 ERRO NO PAGAMENTO DE FATURA:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({ error: 'Erro interno do servidor', details: error.message });
  }
});

// Calcular valor da fatura de um banco
router.get('/:id/bill-amount', async (req, res) => {
  try {
    const { id } = req.params;

    // Verificar se o banco existe e pertence ao usuário
    const bank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // 1. Assinaturas ativas do banco (não pagas este mês)
    const unpaidSubscriptions = await prisma.subscription.findMany({
      where: {
        paymentMethod: {
          bankId: id,
          type: 'CREDIT'
        },
        isActive: true,
        isPaidThisMonth: false,
        userId: req.user.id
      },
      include: {
        category: true
      }
    });

    // 2. Transações não pagas com cartão de crédito deste banco
    const unpaidCreditTransactions = await prisma.transaction.findMany({
      where: {
        paymentMethod: {
          bankId: id,
          type: 'CREDIT'
        },
        isPaid: false,
        userId: req.user.id
      },
      include: {
        category: true,
        paymentMethod: true
      }
    });

    // Calcular totais
    const subscriptionsTotal = unpaidSubscriptions.reduce((sum, sub) => sum + sub.amount, 0);
    const transactionsTotal = unpaidCreditTransactions.reduce((sum, trans) => sum + trans.amount, 0);
    const totalBill = subscriptionsTotal + transactionsTotal;

    res.json({
      bankId: id,
      bankName: bank.name,
      totalBill,
      subscriptionsTotal,
      transactionsTotal,
      unpaidSubscriptions: unpaidSubscriptions.map(sub => ({
        id: sub.id,
        name: sub.name,
        amount: sub.amount,
        category: sub.category
      })),
      unpaidTransactions: unpaidCreditTransactions.map(trans => ({
        id: trans.id,
        description: trans.description,
        amount: trans.amount,
        date: trans.date,
        category: trans.category,
        paymentMethod: trans.paymentMethod
      }))
    });

  } catch (error) {
    console.error('Erro ao calcular valor da fatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar bancos disponíveis para pagamento de fatura
router.get('/payment-options', async (req, res) => {
  try {
    // Buscar todos os bancos do usuário com saldo > 0
    const banks = await prisma.bank.findMany({
      where: {
        userId: req.user.id,
        currentBalance: {
          gt: 0
        }
      },
      include: {
        paymentMethods: {
          where: {
            type: {
              in: ['DEBIT', 'PIX', 'CASH']
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.json(banks);
  } catch (error) {
    console.error('Erro ao buscar opções de pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar histórico de faturas pagas
router.get('/bill-history', async (req, res) => {
  try {
    // Buscar transações de pagamento de fatura
    const billPayments = await prisma.transaction.findMany({
      where: {
        userId: req.user.id,
        description: {
          startsWith: 'Pagamento de Fatura'
        },
        isPaid: true
      },
      include: {
        bank: true,
        paymentMethod: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Para cada pagamento de fatura, buscar as transações relacionadas
    const billHistory = await Promise.all(
      billPayments.map(async (payment) => {
        const billBankName = payment.description.replace('Pagamento de Fatura - ', '');

        // Buscar o banco da fatura
        const billBank = await prisma.bank.findFirst({
          where: {
            name: billBankName,
            userId: req.user.id
          }
        });

        if (!billBank) return null;

        // Buscar transações do mesmo dia que foram pagas junto com esta fatura
        const startOfDay = new Date(payment.date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(payment.date);
        endOfDay.setHours(23, 59, 59, 999);

        const relatedTransactions = await prisma.transaction.findMany({
          where: {
            userId: req.user.id,
            bankId: billBank.id,
            date: {
              gte: startOfDay,
              lte: endOfDay
            },
            description: {
              startsWith: 'Pagamento - '
            },
            isPaid: true
          },
          include: {
            category: true,
            paymentMethod: true
          }
        });

        return {
          id: payment.id,
          date: payment.date,
          totalAmount: payment.amount,
          billBank: billBank,
          paymentBank: payment.bank,
          paymentMethod: payment.paymentMethod,
          creditLimit: billBank.creditLimit,
          transactions: relatedTransactions
        };
      })
    );

    // Filtrar resultados nulos
    const validHistory = billHistory.filter(item => item !== null);

    res.json(validHistory);
  } catch (error) {
    console.error('Erro ao buscar histórico de faturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
