import api from './api'

export const routineService = {
  // Listar rotinas
  async getRoutines() {
    const response = await api.get('/routines')
    return response.data
  },

  // Criar rotina
  async createRoutine(routineData) {
    const response = await api.post('/routines', routineData)
    return response.data
  },

  // Atualizar rotina
  async updateRoutine(id, routineData) {
    const response = await api.put(`/routines/${id}`, routineData)
    return response.data
  },

  // Atualizar apenas o status da rotina
  async updateRoutineStatus(id, isActive) {
    const response = await api.patch(`/routines/${id}/status`, { isActive })
    return response.data
  },

  // Deletar rotina
  async deleteRoutine(id) {
    const response = await api.delete(`/routines/${id}`)
    return response.data
  },

  // Executar rotina (criar transações)
  async executeRoutine(id, data = {}) {
    const response = await api.post(`/routines/${id}/execute`, data)
    return response.data
  },

  // Obter próximas execuções
  async getUpcomingExecutions() {
    const response = await api.get('/routines/upcoming')
    return response.data
  }
}
