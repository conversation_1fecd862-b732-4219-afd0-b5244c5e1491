const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const billService = require('../services/billService');
const installmentService = require('../services/installmentService');
const { BILL_STATUS } = require('../constants/status');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Obter informações da fatura de um banco
router.get('/bank/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;
    
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id },
      include: {
        paymentMethods: {
          where: { type: 'CREDIT' }
        }
      }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Atualizar status da fatura antes de retornar
    await billService.updateBankBillStatus(bank);

    // Buscar banco atualizado
    const updatedBank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    // Buscar transações da fatura atual
    const billTransactions = await prisma.transaction.findMany({
      where: {
        paymentMethod: {
          bankId,
          type: 'CREDIT'
        },
        installmentStatus: 'CURRENT_BILL',
        userId: req.user.id
      },
      include: {
        category: true,
        paymentMethod: true
      },
      orderBy: { date: 'desc' }
    });

    res.json({
      bank: updatedBank,
      transactions: billTransactions,
      totalAmount: updatedBank.currentBillAmount,
      status: updatedBank.billStatus,
      dueDate: updatedBank.billDueDate,
      canPay: updatedBank.currentBillAmount > 0
    });

  } catch (error) {
    console.error('Erro ao obter fatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Pagar fatura
router.post('/bank/:bankId/pay', async (req, res) => {
  try {
    const { bankId } = req.params;
    const { paymentBankId } = req.body;

    if (!paymentBankId) {
      return res.status(400).json({ error: 'Banco de pagamento é obrigatório' });
    }

    // Verificar se os bancos pertencem ao usuário
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    const paymentBank = await prisma.bank.findFirst({
      where: { id: paymentBankId, userId: req.user.id }
    });

    if (!bank || !paymentBank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const result = await billService.payBill(bankId, paymentBankId, req.user.id);
    res.json(result);

  } catch (error) {
    console.error('Erro ao pagar fatura:', error);
    res.status(500).json({ 
      error: error.message || 'Erro interno do servidor' 
    });
  }
});

// Atualizar status de todas as faturas
router.post('/update-statuses', async (req, res) => {
  try {
    const result = await billService.updateBillStatuses();
    res.json(result);
  } catch (error) {
    console.error('Erro ao atualizar status das faturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Verificar se pode criar transação
router.get('/can-create-transaction/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;
    
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const result = await billService.canCreateTransaction(bankId, req.user.id);
    res.json(result);

  } catch (error) {
    console.error('Erro ao verificar criação de transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter histórico de faturas
router.get('/history/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [history, total] = await Promise.all([
      prisma.billHistory.findMany({
        where: { bankId, userId: req.user.id },
        include: {
          paymentBank: true
        },
        orderBy: { paidDate: 'desc' },
        skip,
        take: parseInt(limit)
      }),
      prisma.billHistory.count({
        where: { bankId, userId: req.user.id }
      })
    ]);

    res.json({
      history,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Erro ao obter histórico:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter resumo de todas as faturas do usuário
router.get('/summary', async (req, res) => {
  try {
    const banks = await prisma.bank.findMany({
      where: { 
        userId: req.user.id,
        billDueDay: { not: null }
      }
    });

    // Atualizar status de todas as faturas
    for (const bank of banks) {
      await billService.updateBankBillStatus(bank);
    }

    // Buscar bancos atualizados
    const updatedBanks = await prisma.bank.findMany({
      where: { 
        userId: req.user.id,
        billDueDay: { not: null }
      },
      include: {
        paymentMethods: {
          where: { type: 'CREDIT' }
        }
      }
    });

    const summary = {
      totalBanks: updatedBanks.length,
      pendingBills: updatedBanks.filter(b => b.billStatus === BILL_STATUS.PENDING).length,
      overdueBills: updatedBanks.filter(b => b.billStatus === BILL_STATUS.OVERDUE).length,
      paidBills: updatedBanks.filter(b => b.billStatus === BILL_STATUS.PAID).length,
      totalPendingAmount: updatedBanks
        .filter(b => b.billStatus === BILL_STATUS.PENDING)
        .reduce((sum, b) => sum + b.currentBillAmount, 0),
      totalOverdueAmount: updatedBanks
        .filter(b => b.billStatus === BILL_STATUS.OVERDUE)
        .reduce((sum, b) => sum + b.currentBillAmount, 0),
      banks: updatedBanks.map(bank => ({
        id: bank.id,
        name: bank.name,
        icon: bank.icon,
        color: bank.color,
        billStatus: bank.billStatus,
        currentBillAmount: bank.currentBillAmount,
        billDueDate: bank.billDueDate,
        billDueDay: bank.billDueDay,
        creditCards: bank.paymentMethods.length
      }))
    };

    res.json(summary);

  } catch (error) {
    console.error('Erro ao obter resumo das faturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Forçar recálculo da fatura de um banco
router.post('/recalculate/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const newAmount = await billService.calculateCurrentBillAmount(bankId);

    await prisma.bank.update({
      where: { id: bankId },
      data: { currentBillAmount: newAmount }
    });

    res.json({
      success: true,
      newAmount,
      message: 'Fatura recalculada com sucesso'
    });

  } catch (error) {
    console.error('Erro ao recalcular fatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// REGRA 1: Forçar processamento de parcelamentos reservados
router.post('/process-reserved/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const result = await billService.processReservedInstallments(bankId);

    res.json({
      success: true,
      ...result,
      message: 'Parcelamentos reservados processados com sucesso'
    });

  } catch (error) {
    console.error('Erro ao processar parcelamentos reservados:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter estatísticas de parcelamentos de um banco
router.get('/installments/stats/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const stats = await installmentService.getInstallmentStats(bankId, req.user.id);

    res.json({
      success: true,
      bank: {
        id: bank.id,
        name: bank.name,
        billStatus: bank.billStatus,
        currentBillAmount: bank.currentBillAmount,
        billDueDate: bank.billDueDate
      },
      stats
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas de parcelamentos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Testar status após pagamento de fatura
router.get('/test-payment-status/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Buscar transações CURRENT_BILL vs PAID
    const transactionStats = await prisma.transaction.groupBy({
      by: ['installmentStatus'],
      where: {
        paymentMethod: {
          bankId,
          type: 'CREDIT'
        },
        userId: req.user.id,
        parentTransactionId: { not: null } // Apenas parcelas
      },
      _count: { id: true },
      _sum: { amount: true }
    });

    // Buscar assinaturas por status
    const subscriptionStats = await prisma.subscription.groupBy({
      by: ['status'],
      where: {
        paymentMethod: {
          bankId,
          type: 'CREDIT'
        },
        userId: req.user.id,
        isActive: true
      },
      _count: { id: true },
      _sum: { amount: true }
    });

    res.json({
      bank: {
        id: bank.id,
        name: bank.name,
        billStatus: bank.billStatus,
        currentBillAmount: bank.currentBillAmount
      },
      transactions: transactionStats,
      subscriptions: subscriptionStats,
      message: 'Status de pagamento verificado'
    });

  } catch (error) {
    console.error('Erro ao verificar status de pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
