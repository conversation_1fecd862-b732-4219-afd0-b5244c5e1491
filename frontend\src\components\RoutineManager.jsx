import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit3,
  Trash2,
  Calendar,
  Clock,
  DollarSign,
  Repeat,
  Play,
  Pause,
  CheckCircle,
  Tag,
  User
} from 'lucide-react'
import { routineService } from '../services/routineService'
import { categoryService } from '../services/categoryService'
import { bankService } from '../services/bankService'
import api from '../services/api'
import CurrencyInput from './CurrencyInput'
import TagSelector from './TagSelector'
import toast from 'react-hot-toast'

function RoutineManager() {
  const [routines, setRoutines] = useState([])
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [tags, setTags] = useState([])
  const [contacts, setContacts] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingRoutine, setEditingRoutine] = useState(null)
  const [showItemModal, setShowItemModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [selectedRoutine, setSelectedRoutine] = useState(null)
  const [showExecuteModal, setShowExecuteModal] = useState(false)
  const [executingRoutine, setExecutingRoutine] = useState(null)
  const [itemValues, setItemValues] = useState([])

  const [newRoutine, setNewRoutine] = useState({
    name: '',
    description: '',
    executionDay: 1,
    isActive: true,
    items: []
  })

  const [newItem, setNewItem] = useState({
    name: '',
    type: 'EXPENSE',
    categoryId: '',
    bankId: '',
    description: '',
    transactionContactId: '',
    tags: []
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      console.log('🔄 Carregando dados para RoutineManager...')
      const [routinesData, categoriesData, banksData, tagsData, contactsData] = await Promise.all([
        routineService.getRoutines(),
        categoryService.getCategories(),
        bankService.getBanks(),
        api.get('/tags').then(res => res.data),
        api.get('/transaction-contacts').then(res => res.data)
      ])
      console.log('📊 Dados carregados:', {
        routines: routinesData?.length || 0,
        categories: categoriesData?.length || 0,
        banks: banksData?.length || 0,
        tags: tagsData?.length || 0,
        contacts: contactsData?.length || 0
      })
      setRoutines(routinesData)
      setCategories(categoriesData)
      setBanks(banksData)
      setTags(tagsData)
      setContacts(contactsData)
    } catch (error) {
      console.error('❌ Erro ao carregar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateRoutine = async () => {
    try {
      if (!newRoutine.name) {
        toast.error('Nome da rotina é obrigatório')
        return
      }

      if (editingRoutine) {
        await routineService.updateRoutine(editingRoutine.id, newRoutine)
        toast.success('Rotina atualizada com sucesso!')
      } else {
        await routineService.createRoutine(newRoutine)
        toast.success('Rotina criada com sucesso!')
      }

      setShowModal(false)
      setEditingRoutine(null)
      setNewRoutine({ name: '', description: '', executionDay: 1, isActive: true, items: [] })
      fetchData()
    } catch (error) {
      toast.error('Erro ao salvar rotina')
    }
  }

  const handleDeleteRoutine = async (id) => {
    if (!confirm('Tem certeza que deseja excluir esta rotina?')) return

    try {
      await routineService.deleteRoutine(id)
      toast.success('Rotina excluída com sucesso!')
      fetchData()
    } catch (error) {
      toast.error('Erro ao excluir rotina')
    }
  }

  const handleToggleRoutine = async (routine) => {
    try {
      await routineService.updateRoutineStatus(routine.id, !routine.isActive)
      toast.success(`Rotina ${routine.isActive ? 'desativada' : 'ativada'} com sucesso!`)
      fetchData()
    } catch (error) {
      toast.error('Erro ao alterar status da rotina')
    }
  }

  const handleExecuteRoutine = (routine) => {
    setExecutingRoutine(routine)
    setItemValues(routine.items.map(item => ({ itemId: item.id, amount: 0 })))
    setShowExecuteModal(true)
  }

  const executeRoutineWithValues = async () => {
    try {
      await routineService.executeRoutine(executingRoutine.id, { itemValues })
      toast.success('Rotina executada com sucesso! Transações criadas.')
      setShowExecuteModal(false)
      setExecutingRoutine(null)
      setItemValues([])
      fetchData()
    } catch (error) {
      toast.error('Erro ao executar rotina')
    }
  }

  const handleAddItem = () => {
    if (!newItem.name || !newItem.categoryId || !newItem.bankId) {
      toast.error('Nome, categoria e banco são obrigatórios')
      return
    }

    const item = { ...newItem, id: Date.now() }
    setNewRoutine(prev => ({
      ...prev,
      items: [...prev.items, item]
    }))
    setNewItem({ name: '', type: 'EXPENSE', categoryId: '', bankId: '', description: '', transactionContactId: '', tags: [] })
    setShowItemModal(false)
  }

  const handleRemoveItem = (itemId) => {
    setNewRoutine(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }))
  }

  const handleTagToggle = (tagId) => {
    setNewItem(prev => ({
      ...prev,
      tags: prev.tags.includes(tagId)
        ? prev.tags.filter(id => id !== tagId)
        : [...prev.tags, tagId]
    }))
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getNextExecution = (executionDay) => {
    const now = new Date()
    const nextExecution = new Date(now.getFullYear(), now.getMonth(), executionDay)
    
    if (nextExecution <= now) {
      nextExecution.setMonth(nextExecution.getMonth() + 1)
    }
    
    return nextExecution.toLocaleDateString('pt-BR')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Rotinas de Transações</h2>
          <p className="text-gray-600">Automatize suas transações recorrentes mensais</p>
        </div>
        <button
          onClick={() => {
            setEditingRoutine(null)
            setNewRoutine({ name: '', description: '', executionDay: 1, isActive: true, items: [] })
            setShowModal(true)
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Nova Rotina
        </button>
      </div>

      {/* Lista de Rotinas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {routines.map((routine) => (
          <div key={routine.id} className={`bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] ${
            routine.isActive
              ? 'border-green-200 hover:border-green-300'
              : 'border-gray-200 hover:border-gray-300'
          }`}>
            {/* Header do Card */}
            <div className="p-6 pb-4">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`w-3 h-3 rounded-full ${routine.isActive ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <h3 className="text-xl font-bold text-gray-900">{routine.name}</h3>
                  </div>
                  <div className="flex items-center gap-2 mb-3">
                    <span className={`px-3 py-1 text-xs font-semibold rounded-full ${
                      routine.isActive
                        ? 'bg-green-100 text-green-700 border border-green-200'
                        : 'bg-gray-100 text-gray-600 border border-gray-200'
                    }`}>
                      {routine.isActive ? '🟢 Ativa' : '⚫ Inativa'}
                    </span>
                    <span className="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-full border border-blue-200">
                      📅 Dia {routine.executionDay}
                    </span>
                  </div>
                  {routine.description && (
                    <p className="text-sm text-gray-600 mb-4 leading-relaxed">{routine.description}</p>
                  )}
                </div>
              </div>

              {/* Estatísticas */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="bg-white rounded-xl p-3 border border-gray-100">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="text-xs text-gray-500">Próxima Execução</p>
                      <p className="text-sm font-semibold text-gray-900">{getNextExecution(routine.executionDay)}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white rounded-xl p-3 border border-gray-100">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="text-xs text-gray-500">Total de Itens</p>
                      <p className="text-sm font-semibold text-gray-900">{routine.items?.length || 0} itens</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Itens da Rotina */}
              {routine.items && routine.items.length > 0 && (
                <div className="bg-white rounded-xl p-4 border border-gray-100">
                  <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    📋 Itens da Rotina
                  </h4>
                  <div className="space-y-3">
                    {routine.items.slice(0, 3).map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3 flex-1">
                          <div className={`w-2 h-2 rounded-full ${
                            item.type === 'INCOME' ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900">{item.name}</span>
                            {item.paymentLink && (
                              <a
                                href={item.paymentLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-2 text-blue-500 hover:text-blue-700 text-xs"
                                title="Link de Pagamento"
                              >
                                🔗
                              </a>
                            )}
                          </div>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          item.type === 'INCOME'
                            ? 'bg-green-100 text-green-700'
                            : 'bg-red-100 text-red-700'
                        }`}>
                          {item.type === 'INCOME' ? '💰' : '💸'}
                        </span>
                      </div>
                    ))}
                    {routine.items.length > 3 && (
                      <div className="text-center py-2">
                        <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                          +{routine.items.length - 3} itens adicionais
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Ações */}
            <div className="px-6 pb-6">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleExecuteRoutine(routine)}
                  disabled={!routine.isActive}
                  className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-xl font-semibold transition-all duration-200 ${
                    routine.isActive
                      ? 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                  title="Executar rotina agora"
                >
                  <CheckCircle className="h-4 w-4" />
                  Executar
                </button>

                <div className="flex items-center gap-1">
                  <button
                    onClick={() => handleToggleRoutine(routine)}
                    className={`p-3 rounded-xl transition-all duration-200 ${
                      routine.isActive
                        ? 'bg-orange-100 text-orange-600 hover:bg-orange-200'
                        : 'bg-green-100 text-green-600 hover:bg-green-200'
                    }`}
                    title={routine.isActive ? 'Desativar rotina' : 'Ativar rotina'}
                  >
                    {routine.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </button>

                  <button
                    onClick={() => {
                      setEditingRoutine(routine)
                      setNewRoutine(routine)
                      setShowModal(true)
                    }}
                    className="p-3 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded-xl transition-all duration-200"
                    title="Editar rotina"
                  >
                    <Edit3 className="h-4 w-4" />
                  </button>

                  <button
                    onClick={() => handleDeleteRoutine(routine.id)}
                    className="p-3 bg-red-100 text-red-600 hover:bg-red-200 rounded-xl transition-all duration-200"
                    title="Excluir rotina"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {routines.length === 0 && (
        <div className="text-center py-12">
          <Repeat className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma rotina cadastrada</h3>
          <p className="text-gray-600 mb-4">Crie rotinas para automatizar suas transações recorrentes</p>
          <button
            onClick={() => setShowModal(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Criar Primeira Rotina
          </button>
        </div>
      )}

      {/* Modal de Criação/Edição de Rotina */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {editingRoutine ? 'Editar Rotina' : 'Nova Rotina'}
              </h3>
            </div>

            <div className="p-6 space-y-6">
              {/* Nome da Rotina */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome da Rotina
                </label>
                <input
                  type="text"
                  value={newRoutine.name}
                  onChange={(e) => setNewRoutine({ ...newRoutine, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Contas Mensais"
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição (Opcional)
                </label>
                <textarea
                  value={newRoutine.description || ''}
                  onChange={(e) => setNewRoutine({ ...newRoutine, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição da rotina..."
                  rows={3}
                />
              </div>

              {/* Dia de Execução */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dia de Execução
                </label>
                <select
                  value={newRoutine.executionDay}
                  onChange={(e) => setNewRoutine({ ...newRoutine, executionDay: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                    <option key={day} value={day}>Dia {day}</option>
                  ))}
                </select>
              </div>

              {/* Status */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={newRoutine.isActive}
                  onChange={(e) => setNewRoutine({ ...newRoutine, isActive: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                  Rotina ativa
                </label>
              </div>

              {/* Itens da Rotina */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-gray-900">Itens da Rotina</h4>
                  <button
                    onClick={() => setShowItemModal(true)}
                    className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                  >
                    <Plus className="h-4 w-4" />
                    Adicionar Item
                  </button>
                </div>

                {newRoutine.items && newRoutine.items.length > 0 ? (
                  <div className="space-y-3">
                    {newRoutine.items.map((item, index) => (
                      <div key={item.id || index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-medium text-gray-900">{item.name}</span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              item.type === 'INCOME'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {item.type === 'INCOME' ? 'Receita' : 'Despesa'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {categories.find(c => c.id === item.categoryId)?.name} • {banks.find(b => b.id === item.bankId)?.name}
                            {item.paymentLink && (
                              <span className="ml-2">
                                • <a href={item.paymentLink} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:text-blue-700">Link de Pagamento</a>
                              </span>
                            )}
                          </div>
                        </div>
                        <button
                          onClick={() => handleRemoveItem(item.id || index)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p>Nenhum item adicionado ainda</p>
                    <p className="text-sm">Clique em "Adicionar Item" para começar</p>
                  </div>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowModal(false)
                  setEditingRoutine(null)
                  setNewRoutine({ name: '', description: '', executionDay: 1, isActive: true, items: [] })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateRoutine}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingRoutine ? 'Atualizar' : 'Criar'} Rotina
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Adicionar Item */}
      {showItemModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">Adicionar Item</h3>
            </div>

            <div className="p-6 space-y-4">
              {/* Nome do Item */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome do Item
                </label>
                <input
                  type="text"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Conta de Luz"
                />
              </div>

              {/* Tipo */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo
                </label>
                <select
                  value={newItem.type}
                  onChange={(e) => setNewItem({ ...newItem, type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="EXPENSE">Despesa</option>
                  <option value="INCOME">Receita</option>
                </select>
              </div>

              {/* Contato */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <User className="inline h-4 w-4 mr-1" />
                  Contato (Opcional)
                </label>
                <select
                  value={newItem.transactionContactId}
                  onChange={(e) => setNewItem({ ...newItem, transactionContactId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um contato</option>
                  {contacts.map((contact) => (
                    <option key={contact.id} value={contact.id}>
                      {contact.name} {contact.description && `- ${contact.description}`}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  💡 Identifique quem enviou (receita) ou recebeu (despesa/investimento) esta transação
                </p>
              </div>

              {/* Categoria */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categoria
                </label>
                <select
                  value={newItem.categoryId}
                  onChange={(e) => setNewItem({ ...newItem, categoryId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Banco */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Banco
                </label>
                <select
                  value={newItem.bankId}
                  onChange={(e) => setNewItem({ ...newItem, bankId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição (Opcional)
                </label>
                <input
                  type="text"
                  value={newItem.description}
                  onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição adicional..."
                />
              </div>

              {/* Tags */}
              <TagSelector
                tags={tags}
                selectedTags={newItem.tags}
                onTagToggle={handleTagToggle}
                label="Tags"
              />
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowItemModal(false)
                  setNewItem({ name: '', type: 'EXPENSE', categoryId: '', bankId: '', description: '', transactionContactId: '', tags: [] })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddItem}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Adicionar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Execução de Rotina */}
      {showExecuteModal && executingRoutine && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                Executar Rotina: {executingRoutine.name}
              </h3>
              <p className="text-gray-600 mt-1">
                Informe os valores para cada item da rotina
              </p>
            </div>

            <div className="p-6 space-y-4">
              {executingRoutine.items.map((item, index) => (
                <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.name}</h4>
                      <div className="text-sm text-gray-600 mt-1">
                        {categories.find(c => c.id === item.categoryId)?.name} • {banks.find(b => b.id === item.bankId)?.name}
                        {item.paymentLink && (
                          <span className="ml-2">
                            • <a href={item.paymentLink} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:text-blue-700">Link de Pagamento</a>
                          </span>
                        )}
                      </div>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      item.type === 'INCOME' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {item.type === 'INCOME' ? 'Receita' : 'Despesa'}
                    </span>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Valor
                    </label>
                    <CurrencyInput
                      value={itemValues.find(v => v.itemId === item.id)?.amount || 0}
                      onChange={(value) => {
                        setItemValues(prev =>
                          prev.map(v =>
                            v.itemId === item.id
                              ? { ...v, amount: value }
                              : v
                          )
                        )
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="R$ 0,00"
                    />
                  </div>
                </div>
              ))}
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowExecuteModal(false)
                  setExecutingRoutine(null)
                  setItemValues([])
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={executeRoutineWithValues}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Executar Rotina
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default RoutineManager
