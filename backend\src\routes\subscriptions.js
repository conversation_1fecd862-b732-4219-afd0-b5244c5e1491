const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar assinaturas do usuário
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(subscriptions);
  } catch (error) {
    console.error('Erro ao buscar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar nova assinatura (básico)
router.post('/', async (req, res) => {
  try {
    const {
      name,
      description,
      amount,
      billingDay,
      paymentMethodId,
      categoryId
    } = req.body;

    // Validar dados obrigatórios
    if (!name || !amount || !billingDay || !paymentMethodId) {
      return res.status(400).json({
        error: 'Dados obrigatórios: name, amount, billingDay, paymentMethodId'
      });
    }

    // Verificar se o método de pagamento existe
    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: {
        id: paymentMethodId,
        userId: req.user.id
      },
      include: { bank: true }
    });

    if (!paymentMethod) {
      return res.status(404).json({
        error: 'Método de pagamento não encontrado'
      });
    }

    // Criar assinatura básica
    const subscription = await prisma.subscription.create({
      data: {
        name,
        description,
        amount: parseFloat(amount),
        billingDay: parseInt(billingDay),
        paymentMethodId,
        categoryId: categoryId || null,
        userId: req.user.id,
        startDate: new Date(),
        isActive: true
      },
      include: {
        paymentMethod: {
          include: { bank: true }
        },
        category: true
      }
    });

    res.status(201).json({
      success: true,
      subscription,
      message: 'Assinatura criada com sucesso'
    });
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar assinatura
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, amount, isActive, categoryId } = req.body;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        name: name || subscription.name,
        description: description !== undefined ? description : subscription.description,
        amount: amount !== undefined ? parseFloat(amount) : subscription.amount,
        isActive: isActive !== undefined ? isActive : subscription.isActive,
        categoryId: categoryId !== undefined ? categoryId : subscription.categoryId
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar assinatura
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    console.log('🗑️ Deletando assinatura:', subscription.name);

    // Deletar a assinatura (básico)
    await prisma.subscription.delete({
      where: { id }
    });

    console.log('✅ Assinatura deletada com sucesso');
    res.json({ message: 'Assinatura excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});



module.exports = router;
