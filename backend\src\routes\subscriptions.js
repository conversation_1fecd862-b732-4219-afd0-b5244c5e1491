const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const subscriptionService = require('../services/subscriptionService');
const { SUBSCRIPTION_STATUS } = require('../constants/status');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar assinaturas do usuário
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(subscriptions);
  } catch (error) {
    console.error('Erro ao buscar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar nova assinatura com verificação de regras
router.post('/', async (req, res) => {
  try {
    const {
      name,
      description,
      amount,
      billingDay,
      paymentMethodId,
      categoryId,
      userChoice // 'paid' ou 'not_paid' para REGRA 3
    } = req.body;

    // Validar dados obrigatórios
    if (!name || !amount || !billingDay || !paymentMethodId) {
      return res.status(400).json({
        error: 'Dados obrigatórios: name, amount, billingDay, paymentMethodId'
      });
    }

    // Verificar se o método de pagamento existe e é cartão de crédito
    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: {
        id: paymentMethodId,
        userId: req.user.id,
        type: 'CREDIT'
      },
      include: { bank: true }
    });

    if (!paymentMethod) {
      return res.status(404).json({
        error: 'Método de pagamento não encontrado ou não é cartão de crédito'
      });
    }

    // Preparar dados da assinatura
    const subscriptionData = {
      name,
      description,
      amount: parseFloat(amount),
      billingDay: parseInt(billingDay),
      paymentMethodId,
      categoryId: categoryId || null,
      userId: req.user.id,
      startDate: new Date(),
      nextBillDate: new Date(), // Será calculado pelo serviço
      isActive: true
    };

    // Processar assinatura com as regras de negócio
    const result = await subscriptionService.processSubscription(
      subscriptionData,
      paymentMethod.bank.id,
      req.user.id,
      userChoice
    );

    // Se precisa de escolha do usuário (REGRA 3)
    if (result.needsUserChoice) {
      return res.status(200).json({
        needsUserChoice: true,
        message: result.message,
        billingDay,
        amount: parseFloat(amount),
        bankName: paymentMethod.bank.name,
        options: [
          { value: 'paid', label: 'Sim, marcar como já paga (reduzir saldo do banco)' },
          { value: 'not_paid', label: 'Não, criar como pendente' }
        ]
      });
    }

    // Incluir dados relacionados na resposta
    const subscription = await prisma.subscription.findFirst({
      where: { id: result.subscription.id },
      include: {
        paymentMethod: {
          include: { bank: true }
        },
        category: true
      }
    });

    res.status(201).json({
      success: true,
      subscription,
      statusInfo: result.statusInfo,
      message: 'Assinatura criada com sucesso'
    });
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar assinatura
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, amount, isActive, categoryId } = req.body;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        name: name || subscription.name,
        description: description !== undefined ? description : subscription.description,
        amount: amount !== undefined ? parseFloat(amount) : subscription.amount,
        isActive: isActive !== undefined ? isActive : subscription.isActive,
        categoryId: categoryId !== undefined ? categoryId : subscription.categoryId
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar assinatura
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    console.log('🗑️ Deletando assinatura:', subscription.name);
    console.log('Status de pagamento:', subscription.isPaidThisMonth);

    await prisma.$transaction(async (tx) => {
      // Só reduzir fatura se a assinatura NÃO foi paga este mês
      // Se já foi paga, não deve afetar a fatura atual
      if (!subscription.isPaidThisMonth) {
        await tx.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: {
              decrement: subscription.amount
            }
          }
        });
        console.log('✅ Valor removido da fatura (assinatura não paga)');
      } else {
        console.log('ℹ️ Assinatura já paga - não afetando fatura atual');
      }

      // Deletar a assinatura
      await tx.subscription.delete({
        where: { id }
      });
    });

    console.log('✅ Assinatura deletada com sucesso');
    res.json({ message: 'Assinatura excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Processar assinaturas baseado na data de cobrança
router.post('/process-bills', async (req, res) => {
  try {
    const today = new Date();
    const currentDay = today.getDate();
    const currentMonth = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0');

    // Buscar assinaturas que devem ser cobradas hoje e ainda não foram cobradas este mês
    const subscriptions = await prisma.subscription.findMany({
      where: {
        isActive: true,
        billingDay: currentDay,
        OR: [
          { lastBilledMonth: null },
          { lastBilledMonth: { not: currentMonth } }
        ]
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    let processedCount = 0;

    for (const subscription of subscriptions) {
      try {
        // Adicionar valor à fatura do cartão
        await prisma.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: {
              increment: subscription.amount
            },
            isBillPaid: false
          }
        });

        // Marcar como cobrado este mês
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            lastBilledMonth: currentMonth,
            isPaidThisMonth: false
          }
        });

        processedCount++;
      } catch (error) {
        console.error(`Erro ao processar assinatura ${subscription.id}:`, error);
      }
    }

    res.json({
      message: `${processedCount} assinaturas processadas com sucesso`,
      processedCount
    });
  } catch (error) {
    console.error('Erro ao processar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Verificar e processar assinaturas do dia atual automaticamente
router.post('/check-daily-bills', async (req, res) => {
  try {
    const today = new Date();
    const currentDay = today.getDate();
    const currentMonth = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0');

    // Buscar assinaturas que devem ser cobradas hoje
    const subscriptionsToProcess = await prisma.subscription.findMany({
      where: {
        isActive: true,
        billingDay: currentDay,
        OR: [
          { lastBilledMonth: null },
          { lastBilledMonth: { not: currentMonth } }
        ]
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    let processedCount = 0;

    for (const subscription of subscriptionsToProcess) {
      try {
        // Adicionar valor à fatura do cartão
        await prisma.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: {
              increment: subscription.amount
            },
            isBillPaid: false
          }
        });

        // Marcar como cobrado este mês
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            lastBilledMonth: currentMonth,
            isPaidThisMonth: false
          }
        });

        processedCount++;
      } catch (error) {
        console.error(`Erro ao processar assinatura ${subscription.id}:`, error);
      }
    }

    res.json({
      message: `${processedCount} assinaturas processadas automaticamente`,
      processedCount,
      subscriptions: subscriptionsToProcess.map(s => ({
        id: s.id,
        name: s.name,
        amount: s.amount,
        billingDay: s.billingDay
      }))
    });
  } catch (error) {
    console.error('Erro ao verificar assinaturas diárias:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter estatísticas de assinaturas de um banco
router.get('/stats/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const stats = await subscriptionService.getSubscriptionStats(bankId, req.user.id);

    res.json({
      success: true,
      bank: {
        id: bank.id,
        name: bank.name,
        billStatus: bank.billStatus,
        currentBillAmount: bank.currentBillAmount,
        billDueDate: bank.billDueDate
      },
      stats
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas de assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Forçar processamento de assinaturas reservadas
router.post('/process-reserved', async (req, res) => {
  try {
    const result = await subscriptionService.processReservedSubscriptions();

    res.json({
      success: true,
      ...result,
      message: 'Assinaturas reservadas processadas com sucesso'
    });

  } catch (error) {
    console.error('Erro ao processar assinaturas reservadas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
