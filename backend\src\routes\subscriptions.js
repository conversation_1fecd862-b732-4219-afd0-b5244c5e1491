const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar assinaturas do usuário
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(subscriptions);
  } catch (error) {
    console.error('Erro ao buscar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar assinatura
router.post('/', async (req, res) => {
  try {
    const { name, description, amount, startDate, paymentMethodId, bankId, categoryId } = req.body;

    // Aceitar tanto paymentMethodId quanto bankId para compatibilidade
    const finalPaymentMethodId = paymentMethodId || bankId;

    if (!name || !amount || !startDate || !finalPaymentMethodId) {
      return res.status(400).json({ error: 'Nome, valor, data de início e forma de pagamento são obrigatórios' });
    }

    // Se foi enviado bankId, verificar se o banco tem limite de crédito
    let paymentMethod;
    let bank;

    if (bankId && !paymentMethodId) {
      // Verificar se o banco existe e tem limite de crédito
      bank = await prisma.bank.findFirst({
        where: { id: bankId, userId: req.user.id }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      if (!bank.creditLimit || bank.creditLimit <= 0) {
        return res.status(400).json({
          error: 'Banco deve ter limite de crédito configurado para assinaturas'
        });
      }

      const availableLimit = bank.availableLimit || bank.creditLimit;
      if (availableLimit < parseFloat(amount)) {
        return res.status(400).json({
          error: `Limite insuficiente. Disponível: R$ ${availableLimit.toFixed(2)}`
        });
      }

      // Buscar forma de pagamento de cartão de crédito para este banco
      paymentMethod = await prisma.paymentMethod.findFirst({
        where: { bankId, userId: req.user.id, type: 'CREDIT' }
      });

      if (!paymentMethod) {
        // Criar automaticamente uma forma de pagamento de cartão de crédito para assinaturas
        paymentMethod = await prisma.paymentMethod.create({
          data: {
            name: `Cartão ${bank.name}`,
            icon: '💳',
            color: bank.color || '#3B82F6',
            type: 'CREDIT',
            bankId: bankId,
            userId: req.user.id
          }
        });
      }
    } else {
      // Verificar se a forma de pagamento é um cartão de crédito
      paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: finalPaymentMethodId, userId: req.user.id, type: 'CREDIT' },
        include: { bank: true }
      });

      if (!paymentMethod) {
        return res.status(400).json({ error: 'Apenas cartões de crédito são aceitos para assinaturas' });
      }

      bank = paymentMethod.bank;

      // Verificar limite disponível
      const availableLimit = bank.availableLimit || bank.creditLimit || 0;
      if (availableLimit < parseFloat(amount)) {
        return res.status(400).json({
          error: `Limite insuficiente. Disponível: R$ ${availableLimit.toFixed(2)}`
        });
      }
    }

    const nextBillDate = new Date();
    nextBillDate.setDate(bank?.billDueDay || 10); // Usar dia do banco ou padrão 10

    const subscription = await prisma.subscription.create({
      data: {
        name,
        description: description || null,
        amount: parseFloat(amount),
        startDate: new Date(startDate),
        nextBillDate: nextBillDate,
        paymentMethodId: paymentMethod.id,
        categoryId: categoryId || null,
        userId: req.user.id
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    // Atualizar limite disponível do banco imediatamente
    await prisma.bank.update({
      where: { id: bank.id },
      data: {
        availableLimit: {
          decrement: parseFloat(amount)
        }
      }
    });

    // Adicionar valor à fatura da forma de pagamento
    await prisma.paymentMethod.update({
      where: { id: paymentMethod.id },
      data: {
        currentBill: {
          increment: parseFloat(amount)
        },
        isBillPaid: false
      }
    });

    res.status(201).json(subscription);
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar assinatura
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, amount, isActive, categoryId } = req.body;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        name: name || subscription.name,
        description: description !== undefined ? description : subscription.description,
        amount: amount !== undefined ? parseFloat(amount) : subscription.amount,
        isActive: isActive !== undefined ? isActive : subscription.isActive,
        categoryId: categoryId !== undefined ? categoryId : subscription.categoryId
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar assinatura
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    // Restaurar limite disponível do banco
    if (subscription.paymentMethod.bank.creditLimit > 0) {
      await prisma.bank.update({
        where: { id: subscription.paymentMethod.bankId },
        data: {
          availableLimit: {
            increment: subscription.amount
          }
        }
      });
    }

    // Reduzir fatura da forma de pagamento
    await prisma.paymentMethod.update({
      where: { id: subscription.paymentMethodId },
      data: {
        currentBill: {
          decrement: subscription.amount
        }
      }
    });

    await prisma.subscription.delete({
      where: { id }
    });

    res.json({ message: 'Assinatura excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Processar assinaturas vencidas (para ser chamado por um cron job)
router.post('/process-bills', async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Buscar assinaturas que devem ser cobradas hoje
    const subscriptions = await prisma.subscription.findMany({
      where: {
        isActive: true,
        nextBillDate: {
          lte: today
        }
      },
      include: {
        paymentMethod: true,
        category: true
      }
    });

    let processedCount = 0;

    for (const subscription of subscriptions) {
      try {
        // Adicionar valor à fatura do cartão
        await prisma.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: subscription.paymentMethod.currentBill + subscription.amount,
            isBillPaid: false
          }
        });

        // Atualizar limite disponível do banco se for cartão de crédito
        if (subscription.paymentMethod.type === 'CREDIT' && subscription.paymentMethod.bank.creditLimit > 0) {
          await prisma.bank.update({
            where: { id: subscription.paymentMethod.bankId },
            data: {
              availableLimit: {
                decrement: subscription.amount
              }
            }
          });
        }

        // Criar transação automática
        await prisma.transaction.create({
          data: {
            description: `${subscription.name} (Assinatura)`,
            amount: subscription.amount,
            type: 'EXPENSE',
            categoryId: subscription.categoryId,
            paymentMethodId: subscription.paymentMethodId,
            date: today,
            userId: subscription.userId
          }
        });

        // Atualizar próxima data de cobrança (próximo mês)
        const nextBillDate = new Date(subscription.nextBillDate);
        nextBillDate.setMonth(nextBillDate.getMonth() + 1);

        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { nextBillDate }
        });

        processedCount++;
      } catch (error) {
        console.error(`Erro ao processar assinatura ${subscription.id}:`, error);
      }
    }

    res.json({ 
      message: `${processedCount} assinaturas processadas com sucesso`,
      processedCount 
    });
  } catch (error) {
    console.error('Erro ao processar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
