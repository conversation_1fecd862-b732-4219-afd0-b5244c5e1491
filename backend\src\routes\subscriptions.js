const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar assinaturas do usuário
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(subscriptions);
  } catch (error) {
    console.error('Erro ao buscar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar assinatura
router.post('/', async (req, res) => {
  try {
    const { name, description, amount, startDate, billingDay, isPaidThisMonth, paymentMethodId, bankId, categoryId } = req.body;

    // Aceitar tanto paymentMethodId quanto bankId para compatibilidade
    const finalPaymentMethodId = paymentMethodId || bankId;

    if (!name || !amount || !startDate || !billingDay || !finalPaymentMethodId) {
      return res.status(400).json({ error: 'Nome, valor, data de início, dia de cobrança e forma de pagamento são obrigatórios' });
    }

    // Se foi enviado bankId, verificar se o banco tem limite de crédito
    let paymentMethod;
    let bank;

    if (bankId && !paymentMethodId) {
      // Verificar se o banco existe e tem limite de crédito
      bank = await prisma.bank.findFirst({
        where: { id: bankId, userId: req.user.id }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      if (!bank.creditLimit || bank.creditLimit <= 0) {
        return res.status(400).json({
          error: 'Banco deve ter limite de crédito configurado para assinaturas'
        });
      }

      const availableLimit = bank.availableLimit || bank.creditLimit;
      if (availableLimit < parseFloat(amount)) {
        return res.status(400).json({
          error: `Limite insuficiente. Disponível: R$ ${availableLimit.toFixed(2)}`
        });
      }

      // Buscar forma de pagamento de cartão de crédito para este banco
      paymentMethod = await prisma.paymentMethod.findFirst({
        where: { bankId, userId: req.user.id, type: 'CREDIT' }
      });

      if (!paymentMethod) {
        // Criar automaticamente uma forma de pagamento de cartão de crédito para assinaturas
        paymentMethod = await prisma.paymentMethod.create({
          data: {
            name: `Cartão ${bank.name}`,
            icon: '💳',
            color: bank.color || '#3B82F6',
            type: 'CREDIT',
            bankId: bankId,
            userId: req.user.id
          }
        });
      }
    } else {
      // Verificar se a forma de pagamento é um cartão de crédito
      paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: finalPaymentMethodId, userId: req.user.id, type: 'CREDIT' },
        include: { bank: true }
      });

      if (!paymentMethod) {
        return res.status(400).json({ error: 'Apenas cartões de crédito são aceitos para assinaturas' });
      }

      bank = paymentMethod.bank;

      // Verificar limite disponível
      const availableLimit = bank.availableLimit || bank.creditLimit || 0;
      if (availableLimit < parseFloat(amount)) {
        return res.status(400).json({
          error: `Limite insuficiente. Disponível: R$ ${availableLimit.toFixed(2)}`
        });
      }
    }

    const nextBillDate = new Date();
    nextBillDate.setDate(parseInt(billingDay)); // Usar dia especificado

    // Verificar se deve marcar como já cobrado este mês
    const currentDate = new Date();
    const currentMonth = currentDate.getFullYear() + '-' + String(currentDate.getMonth() + 1).padStart(2, '0');
    const shouldMarkAsBilled = isPaidThisMonth || (parseInt(billingDay) < currentDate.getDate());

    const subscription = await prisma.subscription.create({
      data: {
        name,
        description: description || null,
        amount: parseFloat(amount),
        startDate: new Date(startDate),
        nextBillDate: nextBillDate,
        billingDay: parseInt(billingDay),
        lastBilledMonth: shouldMarkAsBilled ? currentMonth : null,
        isPaidThisMonth: isPaidThisMonth || false,
        paymentMethodId: paymentMethod.id,
        categoryId: categoryId || null,
        userId: req.user.id
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    // Se a assinatura já foi cobrada este mês mas não paga, adicionar à fatura
    if (shouldMarkAsBilled && !isPaidThisMonth) {
      await prisma.paymentMethod.update({
        where: { id: paymentMethod.id },
        data: {
          currentBill: {
            increment: parseFloat(amount)
          },
          isBillPaid: false
        }
      });
    }

    // Reservar o limite do banco
    await prisma.bank.update({
      where: { id: bank.id },
      data: {
        availableLimit: {
          decrement: parseFloat(amount)
        }
      }
    });

    res.status(201).json(subscription);
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar assinatura
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, amount, isActive, categoryId } = req.body;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        name: name || subscription.name,
        description: description !== undefined ? description : subscription.description,
        amount: amount !== undefined ? parseFloat(amount) : subscription.amount,
        isActive: isActive !== undefined ? isActive : subscription.isActive,
        categoryId: categoryId !== undefined ? categoryId : subscription.categoryId
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar assinatura
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    console.log('🗑️ Deletando assinatura:', subscription.name);
    console.log('Status de pagamento:', subscription.isPaidThisMonth);

    await prisma.$transaction(async (tx) => {
      // Só reduzir fatura se a assinatura NÃO foi paga este mês
      // Se já foi paga, não deve afetar a fatura atual
      if (!subscription.isPaidThisMonth) {
        await tx.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: {
              decrement: subscription.amount
            }
          }
        });
        console.log('✅ Valor removido da fatura (assinatura não paga)');
      } else {
        console.log('ℹ️ Assinatura já paga - não afetando fatura atual');
      }

      // Deletar a assinatura
      await tx.subscription.delete({
        where: { id }
      });
    });

    console.log('✅ Assinatura deletada com sucesso');
    res.json({ message: 'Assinatura excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Processar assinaturas baseado na data de cobrança
router.post('/process-bills', async (req, res) => {
  try {
    const today = new Date();
    const currentDay = today.getDate();
    const currentMonth = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0');

    // Buscar assinaturas que devem ser cobradas hoje e ainda não foram cobradas este mês
    const subscriptions = await prisma.subscription.findMany({
      where: {
        isActive: true,
        billingDay: currentDay,
        OR: [
          { lastBilledMonth: null },
          { lastBilledMonth: { not: currentMonth } }
        ]
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    let processedCount = 0;

    for (const subscription of subscriptions) {
      try {
        // Adicionar valor à fatura do cartão
        await prisma.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: {
              increment: subscription.amount
            },
            isBillPaid: false
          }
        });

        // Marcar como cobrado este mês
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            lastBilledMonth: currentMonth,
            isPaidThisMonth: false
          }
        });

        processedCount++;
      } catch (error) {
        console.error(`Erro ao processar assinatura ${subscription.id}:`, error);
      }
    }

    res.json({
      message: `${processedCount} assinaturas processadas com sucesso`,
      processedCount
    });
  } catch (error) {
    console.error('Erro ao processar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Verificar e processar assinaturas do dia atual automaticamente
router.post('/check-daily-bills', async (req, res) => {
  try {
    const today = new Date();
    const currentDay = today.getDate();
    const currentMonth = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0');

    // Buscar assinaturas que devem ser cobradas hoje
    const subscriptionsToProcess = await prisma.subscription.findMany({
      where: {
        isActive: true,
        billingDay: currentDay,
        OR: [
          { lastBilledMonth: null },
          { lastBilledMonth: { not: currentMonth } }
        ]
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    let processedCount = 0;

    for (const subscription of subscriptionsToProcess) {
      try {
        // Adicionar valor à fatura do cartão
        await prisma.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: {
              increment: subscription.amount
            },
            isBillPaid: false
          }
        });

        // Marcar como cobrado este mês
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            lastBilledMonth: currentMonth,
            isPaidThisMonth: false
          }
        });

        processedCount++;
      } catch (error) {
        console.error(`Erro ao processar assinatura ${subscription.id}:`, error);
      }
    }

    res.json({
      message: `${processedCount} assinaturas processadas automaticamente`,
      processedCount,
      subscriptions: subscriptionsToProcess.map(s => ({
        id: s.id,
        name: s.name,
        amount: s.amount,
        billingDay: s.billingDay
      }))
    });
  } catch (error) {
    console.error('Erro ao verificar assinaturas diárias:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
