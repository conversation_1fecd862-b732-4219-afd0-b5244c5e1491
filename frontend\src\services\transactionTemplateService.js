import api from './api'

export const transactionTemplateService = {
  // Listar todos os templates
  async getTemplates() {
    const response = await api.get('/transactiontemplates')
    return response.data
  },

  // Listar templates ativos
  async getActiveTemplates() {
    const response = await api.get('/transactiontemplates/active')
    return response.data
  },

  // Criar template
  async createTemplate(templateData) {
    const response = await api.post('/transactiontemplates', templateData)
    return response.data
  },

  // Atualizar template
  async updateTemplate(id, templateData) {
    const response = await api.put(`/transactiontemplates/${id}`, templateData)
    return response.data
  },

  // Deletar template
  async deleteTemplate(id) {
    const response = await api.delete(`/transactiontemplates/${id}`)
    return response.data
  },

  // Usar template para criar transação
  async useTemplate(id, transactionData) {
    const response = await api.post(`/transactiontemplates/${id}/use`, transactionData)
    return response.data
  }
}
