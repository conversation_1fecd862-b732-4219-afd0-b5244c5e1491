const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar rotinas
router.get('/', async (req, res) => {
  try {
    const routines = await prisma.routine.findMany({
      where: { userId: req.user.id },
      include: {
        items: {
          include: {
            category: true,
            bank: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(routines);
  } catch (error) {
    console.error('Erro ao buscar rotinas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar rotina
router.post('/', async (req, res) => {
  try {
    const { name, description, executionDay, isActive, items } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }

    if (!executionDay || executionDay < 1 || executionDay > 31) {
      return res.status(400).json({ error: 'Dia de execução deve estar entre 1 e 31' });
    }

    const routine = await prisma.routine.create({
      data: {
        name,
        description,
        executionDay,
        isActive: isActive !== undefined ? isActive : true,
        userId: req.user.id,
        items: {
          create: items?.map(item => ({
            name: item.name,
            type: item.type,
            description: item.description,
            paymentLink: item.paymentLink,
            categoryId: item.categoryId,
            bankId: item.bankId
          })) || []
        }
      },
      include: {
        items: {
          include: {
            category: true,
            bank: true
          }
        }
      }
    });

    res.status(201).json(routine);
  } catch (error) {
    console.error('Erro ao criar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar apenas o status da rotina (pausar/ativar)
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    const updatedRoutine = await prisma.routine.update({
      where: { id },
      data: { isActive },
      include: {
        items: {
          include: {
            category: true,
            bank: true
          }
        }
      }
    });

    res.json(updatedRoutine);
  } catch (error) {
    console.error('Erro ao atualizar status da rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar rotina completa
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, executionDay, isActive, items } = req.body;

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    // Atualizar rotina e seus itens
    const updatedRoutine = await prisma.$transaction(async (prisma) => {
      // Deletar itens existentes
      await prisma.routineItem.deleteMany({
        where: { routineId: id }
      });

      // Atualizar rotina
      const updated = await prisma.routine.update({
        where: { id },
        data: {
          name: name || routine.name,
          description: description !== undefined ? description : routine.description,
          executionDay: executionDay || routine.executionDay,
          isActive: isActive !== undefined ? isActive : routine.isActive,
          items: {
            create: items?.map(item => ({
              name: item.name,
              type: item.type,
              description: item.description,
              transactionContactsId: item.transactionContactsId,
              categoryId: item.categoryId,
              bankId: item.bankId
            })) || []
          }
        },
        include: {
          items: {
            include: {
              category: true,
              bank: true,
              transactionContacts: true
            }
          }
        }
      });

      return updated;
    });

    res.json(updatedRoutine);
  } catch (error) {
    console.error('Erro ao atualizar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar rotina
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    await prisma.routine.delete({
      where: { id }
    });

    res.json({ message: 'Rotina deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Executar rotina (criar transações com valores informados)
router.post('/:id/execute', async (req, res) => {
  try {
    const { id } = req.params;
    const { itemValues } = req.body; // Array com { itemId, amount }

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id },
      include: {
        items: {
          include: {
            category: true,
            bank: true
          }
        }
      }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    if (!routine.isActive) {
      return res.status(400).json({ error: 'Rotina está inativa' });
    }

    if (!routine.items || routine.items.length === 0) {
      return res.status(400).json({ error: 'Rotina não possui itens para executar' });
    }

    if (!itemValues || itemValues.length === 0) {
      return res.status(400).json({ error: 'Valores dos itens são obrigatórios' });
    }

    // Criar transações para cada item da rotina
    const transactions = await prisma.$transaction(async (prisma) => {
      const createdTransactions = [];

      for (const item of routine.items) {
        const itemValue = itemValues.find(v => v.itemId === item.id);
        if (!itemValue || !itemValue.amount) {
          continue; // Pular itens sem valor
        }

        const transaction = await prisma.transaction.create({
          data: {
            description: `${routine.name} - ${item.name}`,
            amount: Math.abs(itemValue.amount),
            type: item.type,
            date: new Date(),
            categoryId: item.categoryId,
            bankId: item.bankId,
            userId: req.user.id
          },
          include: {
            category: true,
            bank: true
          }
        });

        // Atualizar saldo do banco
        if (item.bank) {
          const balanceChange = item.type === 'INCOME' ? itemValue.amount : -itemValue.amount;
          await prisma.bank.update({
            where: { id: item.bankId },
            data: {
              currentBalance: {
                increment: balanceChange
              }
            }
          });
        }

        createdTransactions.push(transaction);
      }

      return createdTransactions;
    });

    res.json({
      message: 'Rotina executada com sucesso',
      transactions,
      count: transactions.length
    });
  } catch (error) {
    console.error('Erro ao executar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter próximas execuções
router.get('/upcoming', async (req, res) => {
  try {
    const routines = await prisma.routine.findMany({
      where: { 
        userId: req.user.id,
        isActive: true
      },
      include: {
        items: true
      }
    });

    const now = new Date();
    const upcoming = routines.map(routine => {
      const nextExecution = new Date(now.getFullYear(), now.getMonth(), routine.executionDay);
      
      if (nextExecution <= now) {
        nextExecution.setMonth(nextExecution.getMonth() + 1);
      }

      return {
        ...routine,
        nextExecution,
        daysUntilExecution: Math.ceil((nextExecution - now) / (1000 * 60 * 60 * 24))
      };
    }).sort((a, b) => a.nextExecution - b.nextExecution);

    res.json(upcoming);
  } catch (error) {
    console.error('Erro ao buscar próximas execuções:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
