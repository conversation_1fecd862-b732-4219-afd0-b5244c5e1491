import api from './api'

export const transactionContactservice = {
  // Listar todos os contatos de transações
  async getContacts() {
    try {
      const response = await api.get('/transactionContacts')
      return response.data
    } catch (error) {
      console.error('Erro ao buscar contatos de transações:', error)
      throw error
    }
  },

  // Buscar contato por ID
  async getContactById(id) {
    try {
      const response = await api.get(`/transactionContacts/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar contato:', error)
      throw error
    }
  },

  // Criar novo contato
  async createContact(contactData) {
    try {
      const response = await api.post('/transactionContacts', contactData)
      return response.data
    } catch (error) {
      console.error('Erro ao criar contato:', error)
      throw error
    }
  },

  // Atualizar contato
  async updateContact(id, contactData) {
    try {
      const response = await api.put(`/transactionContacts/${id}`, contactData)
      return response.data
    } catch (error) {
      console.error('Erro ao atualizar contato:', error)
      throw error
    }
  },

  // Deletar contato
  async deleteContact(id) {
    try {
      const response = await api.delete(`/transactionContacts/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao deletar contato:', error)
      throw error
    }
  },

  // Upload de foto do contato
  async uploadPhoto(id, photoFile) {
    try {
      const formData = new FormData()
      formData.append('photo', photoFile)

      const response = await api.post(`/transactionContacts/${id}/photo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('Erro ao fazer upload da foto:', error)
      throw error
    }
  },

  // Buscar estatísticas do contato
  async getContactStats(id, month, year) {
    try {
      const params = {}
      if (month) params.month = month
      if (year) params.year = year

      const response = await api.get(`/transactionContacts/${id}/stats`, { params })
      return response.data
    } catch (error) {
      console.error('Erro ao buscar estatísticas do contato:', error)
      throw error
    }
  }
}

export default transactionContactservice
