import api from './api'

export const transactionContactService = {
  // Listar todos os contatos de transações
  async getContacts() {
    try {
      console.log('🔍 Chamando API: GET /txcontacts')
      const response = await api.get('/txcontacts')
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar contatos de transações:', error)
      throw error
    }
  },

  // Buscar contato por ID
  async getContactById(id) {
    try {
      console.log(`🔍 Chamando API: GET /txcontacts/${id}`)
      const response = await api.get(`/txcontacts/${id}`)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar contato:', error)
      throw error
    }
  },

  // Criar novo contato
  async createContact(contactData) {
    try {
      console.log('🔍 Chamando API: POST /txcontacts', contactData)
      const response = await api.post('/txcontacts', contactData)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('Erro ao criar contato:', error)
      throw error
    }
  },

  // Atualizar contato
  async updateContact(id, contactData) {
    try {
      console.log(`🔍 Chamando API: PUT /txcontacts/${id}`, contactData)
      const response = await api.put(`/txcontacts/${id}`, contactData)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('Erro ao atualizar contato:', error)
      throw error
    }
  },

  // Deletar contato
  async deleteContact(id) {
    try {
      console.log(`🔍 Chamando API: DELETE /txcontacts/${id}`)
      const response = await api.delete(`/txcontacts/${id}`)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('Erro ao deletar contato:', error)
      throw error
    }
  },

  // Upload de foto do contato
  async uploadPhoto(id, photoFile) {
    try {
      const formData = new FormData()
      formData.append('photo', photoFile)

      console.log(`🔍 Chamando API: POST /txcontacts/${id}/photo`)
      const response = await api.post(`/txcontacts/${id}/photo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('Erro ao fazer upload da foto:', error)
      throw error
    }
  },

  // Buscar estatísticas do contato
  async getContactStats(id, month, year) {
    try {
      const params = {}
      if (month) params.month = month
      if (year) params.year = year

      console.log(`🔍 Chamando API: GET /txcontacts/${id}/stats`, params)
      const response = await api.get(`/txcontacts/${id}/stats`, { params })
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas do contato:', error)
      throw error
    }
  }
}

export default transactionContactService
