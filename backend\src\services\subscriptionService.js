const { PrismaClient } = require('@prisma/client');
const dateService = require('./dateService');
const { SUBSCRIPTION_STATUS } = require('../constants/status');

const prisma = new PrismaClient();

class SubscriptionService {
  /**
   * REGRA 0: Processa assinaturas RESERVED que devem ir para CURRENT_BILL
   * Executa automaticamente quando dia de cobrança <= dia atual
   */
  async processReservedSubscriptions() {
    try {
      console.log('🔄 Processando assinaturas reservadas...');

      const currentDate = await dateService.getCurrentDate();
      const currentDay = currentDate.getDate();

      // Buscar assinaturas reservadas que devem ser cobradas
      const reservedSubscriptions = await prisma.subscription.findMany({
        where: {
          status: SUBSCRIPTION_STATUS.PENDING, // RESERVED na verdade
          billingDay: {
            lte: currentDay // Dia de cobrança <= dia atual
          },
          isActive: true
        },
        include: {
          paymentMethod: {
            include: {
              bank: true
            }
          }
        }
      });

      let processedCount = 0;
      let totalAmount = 0;

      for (const subscription of reservedSubscriptions) {
        // Verificar se a fatura do banco está PENDING
        if (subscription.paymentMethod.bank && 
            subscription.paymentMethod.bank.billStatus === 'PENDING') {
          
          await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
              status: SUBSCRIPTION_STATUS.BILLED // CURRENT_BILL
            }
          });

          processedCount++;
          totalAmount += subscription.amount;
          console.log(`💳 Assinatura ${subscription.name} movida para CURRENT_BILL: R$ ${subscription.amount}`);
        }
      }

      if (processedCount > 0) {
        console.log(`✅ ${processedCount} assinaturas processadas - Total: R$ ${totalAmount}`);
      }

      return { success: true, processedCount, totalAmount };

    } catch (error) {
      console.error('❌ Erro ao processar assinaturas reservadas:', error);
      throw error;
    }
  }

  /**
   * REGRAS 2, 3, 4: Determina o status correto de uma assinatura baseado na data
   */
  async determineSubscriptionStatus(billingDay, bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank || !bank.billDueDay) {
        // Se não tem dia de fechamento, vai direto para saldo
        return {
          status: SUBSCRIPTION_STATUS.PAID, // Será processada como BANK_BALANCE
          shouldReduceBankBalance: true,
          shouldGoToBill: false,
          shouldStayReserved: false,
          needsUserChoice: false
        };
      }

      const currentDate = await dateService.getCurrentDate();
      const billDueDate = bank.billDueDate || this.calculateBillDueDate(bank.billDueDay, currentDate);
      
      // Calcular data de fechamento da fatura anterior (1 mês antes)
      const previousBillDueDate = new Date(billDueDate);
      previousBillDueDate.setMonth(previousBillDueDate.getMonth() - 1);

      console.log(`📅 Analisando assinatura com cobrança no dia ${billingDay}:`);
      console.log(`   Fechamento anterior: ${previousBillDueDate.toLocaleDateString()}`);
      console.log(`   Fechamento atual: ${billDueDate.toLocaleDateString()}`);

      // REGRA 3: Dia de cobrança menor que (vencimento - 1 mês) = Perguntar ao usuário
      if (billingDay < previousBillDueDate.getDate()) {
        console.log(`   ✅ REGRA 3: Dia < fechamento anterior → PERGUNTAR AO USUÁRIO`);
        return {
          status: null, // Será definido pela escolha do usuário
          shouldReduceBankBalance: false,
          shouldGoToBill: false,
          shouldStayReserved: false,
          needsUserChoice: true,
          message: 'Assinatura será cobrada antes do período atual. Deseja marcar como já paga?'
        };
      }

      // REGRA 4: Dia dentro do range (fechamento anterior até fechamento atual) = CURRENT_BILL
      if (billingDay >= previousBillDueDate.getDate() && billingDay <= billDueDate.getDate()) {
        console.log(`   ✅ REGRA 4: Dia no período atual → CURRENT_BILL`);
        return {
          status: SUBSCRIPTION_STATUS.BILLED, // CURRENT_BILL
          shouldReduceBankBalance: false,
          shouldGoToBill: true,
          shouldStayReserved: false,
          needsUserChoice: false
        };
      }

      // REGRA 2: Dia maior que fechamento atual = RESERVED
      if (billingDay > billDueDate.getDate()) {
        console.log(`   ✅ REGRA 2: Dia > fechamento atual → RESERVED`);
        return {
          status: SUBSCRIPTION_STATUS.PENDING, // RESERVED
          shouldReduceBankBalance: false,
          shouldGoToBill: false,
          shouldStayReserved: true,
          needsUserChoice: false
        };
      }

      // Fallback
      return {
        status: SUBSCRIPTION_STATUS.PENDING,
        shouldReduceBankBalance: false,
        shouldGoToBill: false,
        shouldStayReserved: false,
        needsUserChoice: false
      };

    } catch (error) {
      console.error('❌ Erro ao determinar status da assinatura:', error);
      throw error;
    }
  }

  /**
   * Calcula a data de vencimento da fatura baseada no dia de vencimento
   */
  calculateBillDueDate(billDueDay, currentDate = null) {
    if (!currentDate) {
      currentDate = new Date();
    }

    const billDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), billDueDay);
    
    // Se a data de vencimento já passou este mês, usar próximo mês
    if (billDueDate <= currentDate) {
      billDueDate.setMonth(billDueDate.getMonth() + 1);
    }

    return billDueDate;
  }

  /**
   * REGRA 1: Calcula o valor total das assinaturas CURRENT_BILL para a fatura
   */
  async calculateSubscriptionBillAmount(bankId) {
    try {
      const subscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          status: SUBSCRIPTION_STATUS.BILLED, // CURRENT_BILL
          isActive: true
        }
      });

      const total = subscriptions.reduce((sum, subscription) => sum + subscription.amount, 0);
      
      console.log(`💰 Valor das assinaturas na fatura (banco ${bankId}): R$ ${total} (${subscriptions.length} assinaturas)`);
      
      return total;
    } catch (error) {
      console.error('❌ Erro ao calcular valor das assinaturas:', error);
      return 0;
    }
  }

  /**
   * REGRA 5: Processa pagamento da fatura - marca assinaturas como PAID
   */
  async processBillPayment(bankId, userId) {
    try {
      console.log(`💳 Processando pagamento de assinaturas - Banco: ${bankId}`);

      // Buscar todas as assinaturas CURRENT_BILL
      const currentBillSubscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          status: SUBSCRIPTION_STATUS.BILLED, // CURRENT_BILL
          userId,
          isActive: true
        }
      });

      let processedCount = 0;
      let totalAmount = 0;

      // Marcar todas como PAID
      for (const subscription of currentBillSubscriptions) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SUBSCRIPTION_STATUS.PAID
          }
        });

        processedCount++;
        totalAmount += subscription.amount;
        console.log(`✅ Assinatura ${subscription.name} marcada como PAID: R$ ${subscription.amount}`);
      }

      console.log(`💳 ${processedCount} assinaturas processadas - Total: R$ ${totalAmount}`);

      return {
        success: true,
        processedCount,
        totalAmount
      };

    } catch (error) {
      console.error('❌ Erro ao processar pagamento de assinaturas:', error);
      throw error;
    }
  }

  /**
   * REGRA 6: Processa transição para próxima fatura - PAID → RESERVED
   */
  async processNextBillCycle(bankId, userId) {
    try {
      console.log(`🔄 Processando ciclo de assinaturas - Banco: ${bankId}`);

      // Buscar todas as assinaturas PAID
      const paidSubscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          status: SUBSCRIPTION_STATUS.PAID,
          userId,
          isActive: true
        }
      });

      let processedCount = 0;

      // Marcar todas como RESERVED (PENDING)
      for (const subscription of paidSubscriptions) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SUBSCRIPTION_STATUS.PENDING // RESERVED
          }
        });

        processedCount++;
        console.log(`🔄 Assinatura ${subscription.name} movida para RESERVED`);
      }

      console.log(`✅ ${processedCount} assinaturas movidas para RESERVED`);

      return {
        success: true,
        processedCount
      };

    } catch (error) {
      console.error('❌ Erro ao processar ciclo de assinaturas:', error);
      throw error;
    }
  }

  /**
   * Processa uma assinatura individual aplicando as regras de negócio
   */
  async processSubscription(subscriptionData, bankId, userId, userChoice = null) {
    try {
      const { billingDay, amount } = subscriptionData;
      
      // Determinar status correto
      const statusInfo = await this.determineSubscriptionStatus(billingDay, bankId, userId);
      
      // Se precisa de escolha do usuário (REGRA 3)
      if (statusInfo.needsUserChoice) {
        if (userChoice === null) {
          return {
            needsUserChoice: true,
            message: statusInfo.message,
            statusInfo
          };
        }

        // Aplicar escolha do usuário
        if (userChoice === 'paid') {
          // Criar como BANK_BALANCE (reduzir saldo)
          statusInfo.status = SUBSCRIPTION_STATUS.PAID;
          statusInfo.shouldReduceBankBalance = true;
        } else {
          // Criar como PAID (não reduzir saldo)
          statusInfo.status = SUBSCRIPTION_STATUS.PAID;
          statusInfo.shouldReduceBankBalance = false;
        }
      }

      // Criar a assinatura
      const subscription = await prisma.subscription.create({
        data: {
          ...subscriptionData,
          status: statusInfo.status
        }
      });

      // Aplicar efeitos baseado no status
      if (statusInfo.shouldReduceBankBalance) {
        // REGRA 3: Reduzir saldo do banco imediatamente
        await prisma.bank.update({
          where: { id: bankId },
          data: {
            currentBalance: {
              decrement: amount
            }
          }
        });
        console.log(`💰 Saldo reduzido em R$ ${amount} (BANK_BALANCE)`);
      }

      if (statusInfo.shouldGoToBill) {
        // REGRA 4: Será incluída na fatura atual
        console.log(`💳 Assinatura adicionada à fatura atual: R$ ${amount} (CURRENT_BILL)`);
      }

      if (statusInfo.shouldStayReserved) {
        // REGRA 2: Fica reservada para próxima fatura
        console.log(`⏳ Assinatura reservada para próxima fatura: R$ ${amount} (RESERVED)`);
      }

      return {
        subscription,
        statusInfo,
        needsUserChoice: false
      };

    } catch (error) {
      console.error('❌ Erro ao processar assinatura:', error);
      throw error;
    }
  }

  /**
   * Obtém estatísticas de assinaturas de um banco
   */
  async getSubscriptionStats(bankId, userId) {
    try {
      const stats = await prisma.subscription.groupBy({
        by: ['status'],
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          userId,
          isActive: true
        },
        _count: {
          id: true
        },
        _sum: {
          amount: true
        }
      });

      const result = {
        total: 0,
        totalAmount: 0,
        byStatus: {}
      };

      for (const stat of stats) {
        const status = stat.status || 'UNKNOWN';
        result.byStatus[status] = {
          count: stat._count.id,
          amount: stat._sum.amount || 0
        };
        result.total += stat._count.id;
        result.totalAmount += stat._sum.amount || 0;
      }

      return result;

    } catch (error) {
      console.error('❌ Erro ao obter estatísticas de assinaturas:', error);
      throw error;
    }
  }
}

const subscriptionService = new SubscriptionService();
module.exports = subscriptionService;
