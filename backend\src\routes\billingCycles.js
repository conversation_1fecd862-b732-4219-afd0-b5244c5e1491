const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const billingCycleService = require('../services/billingCycleService');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Obter informações do ciclo de faturamento de um banco
router.get('/bank/:bankId/info', async (req, res) => {
  try {
    const { bankId } = req.params;
    const info = await billingCycleService.getBillingCycleInfo(bankId, req.user.id);
    res.json(info);
  } catch (error) {
    console.error('Erro ao obter informações do ciclo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Processar ciclos de faturamento manualmente (para testes)
router.post('/process', async (req, res) => {
  try {
    const result = await billingCycleService.processBillingCycles();
    res.json(result);
  } catch (error) {
    console.error('Erro ao processar ciclos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Processar assinaturas diárias automaticamente
router.post('/process-daily', async (req, res) => {
  try {
    console.log('🔄 Iniciando processamento diário...');

    // 1. Processar assinaturas do dia
    const subscriptionResult = await billingCycleService.processDailySubscriptions();

    // 2. Processar ciclos de faturamento (se for dia de fechamento)
    const billingResult = await billingCycleService.processBillingCycles();

    res.json({
      success: true,
      subscriptions: subscriptionResult,
      billing: billingResult,
      message: 'Processamento diário concluído com sucesso'
    });
  } catch (error) {
    console.error('Erro no processamento diário:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Fechar ciclo de faturamento de um cartão específico
router.post('/payment-method/:paymentMethodId/close', async (req, res) => {
  try {
    const { paymentMethodId } = req.params;
    
    // Verificar se o método de pagamento pertence ao usuário
    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: { id: paymentMethodId, userId: req.user.id },
      include: { bank: true }
    });

    if (!paymentMethod) {
      return res.status(404).json({ error: 'Método de pagamento não encontrado' });
    }

    if (!paymentMethod.bank) {
      return res.status(400).json({ error: 'Banco não encontrado para este cartão' });
    }

    const result = await billingCycleService.closeBillingCycle(paymentMethodId, paymentMethod.bank.id);
    res.json(result);
  } catch (error) {
    console.error('Erro ao fechar ciclo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Sincronizar transações com ciclos de faturamento
router.post('/sync-transactions', async (req, res) => {
  try {
    const result = await billingCycleService.syncTransactionsWithBillingCycles(req.user.id);
    res.json(result);
  } catch (error) {
    console.error('Erro ao sincronizar transações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Sincronizar faturas de um banco específico
router.post('/sync-bank-bills/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    // Verificar se o banco pertence ao usuário
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const result = await billingCycleService.syncBankBills(bankId, req.user.id);
    res.json(result);
  } catch (error) {
    console.error('Erro ao sincronizar faturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Verificar se uma data deve ir para fatura ou reduzir saldo
router.post('/check-billing-logic', async (req, res) => {
  try {
    const { transactionDate, bankId } = req.body;
    
    if (!transactionDate || !bankId) {
      return res.status(400).json({ error: 'Data da transação e ID do banco são obrigatórios' });
    }

    // Verificar se o banco pertence ao usuário
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const result = await billingCycleService.shouldGoToBill(transactionDate, bankId, req.user.id);
    res.json(result);
  } catch (error) {
    console.error('Erro ao verificar lógica de faturamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar todas as transações pendentes de processamento
router.get('/pending-transactions', async (req, res) => {
  try {
    const pendingTransactions = await prisma.transaction.findMany({
      where: {
        userId: req.user.id,
        paymentMethod: {
          type: 'CREDIT'
        },
        isPaid: true, // Transações que estão "aguardando"
        parentTransactionId: { not: null } // Apenas parcelas
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      },
      orderBy: { date: 'asc' }
    });

    // Agrupar por banco e adicionar informações de ciclo
    const groupedByBank = {};
    
    for (const transaction of pendingTransactions) {
      const bankId = transaction.paymentMethod.bank?.id;
      if (!bankId) continue;

      if (!groupedByBank[bankId]) {
        const billingInfo = await billingCycleService.getBillingCycleInfo(bankId, req.user.id);
        groupedByBank[bankId] = {
          bank: transaction.paymentMethod.bank,
          billingInfo,
          transactions: []
        };
      }

      // Verificar se deve ir para próxima fatura
      const shouldGoToBill = await billingCycleService.shouldGoToBill(
        transaction.date, 
        bankId, 
        req.user.id
      );

      groupedByBank[bankId].transactions.push({
        ...transaction,
        billingStatus: shouldGoToBill
      });
    }

    res.json(groupedByBank);
  } catch (error) {
    console.error('Erro ao buscar transações pendentes:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Estatísticas de ciclos de faturamento
router.get('/stats', async (req, res) => {
  try {
    const userId = req.user.id;

    // Bancos com ciclo de faturamento
    const banksWithBilling = await prisma.bank.count({
      where: { 
        userId, 
        billDueDay: { not: null } 
      }
    });

    // Cartões de crédito
    const creditCards = await prisma.paymentMethod.count({
      where: { 
        userId, 
        type: 'CREDIT' 
      }
    });

    // Transações aguardando processamento
    const pendingTransactions = await prisma.transaction.count({
      where: {
        userId,
        paymentMethod: {
          type: 'CREDIT'
        },
        isPaid: true,
        parentTransactionId: { not: null }
      }
    });

    // Próximos vencimentos (próximos 7 dias)
    const upcomingDueDates = await prisma.bank.findMany({
      where: {
        userId,
        billDueDay: { not: null }
      },
      select: {
        id: true,
        name: true,
        billDueDay: true
      }
    });

    const upcomingBills = upcomingDueDates.filter(bank => {
      const nextDueDate = billingCycleService.getNextBillDueDate(bank.billDueDay);
      const daysUntilDue = Math.ceil((nextDueDate - new Date()) / (1000 * 60 * 60 * 24));
      return daysUntilDue <= 7;
    });

    res.json({
      banksWithBilling,
      creditCards,
      pendingTransactions,
      upcomingBills: upcomingBills.length,
      upcomingBillDetails: upcomingBills.map(bank => ({
        ...bank,
        nextDueDate: billingCycleService.getNextBillDueDate(bank.billDueDay),
        daysUntilDue: Math.ceil((billingCycleService.getNextBillDueDate(bank.billDueDay) - new Date()) / (1000 * 60 * 60 * 24))
      }))
    });
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
