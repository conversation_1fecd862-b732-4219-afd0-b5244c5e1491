// Status constants for better type safety and consistency

const SUBSCRIPTION_STATUS = {
  PENDING: 'PENDING',       // Aguardando cobrança
  BILLED: 'BILLED',         // Faturada (adicionada à fatura)
  PAID: 'PAID',             // Paga (fatura foi paga)
  OVERDUE: 'OVERDUE',       // Em atraso
  CANCELLED: 'CANCELLED'    // Cancelada
};

const INSTALLMENT_STATUS = {
  PENDING: 'PENDING',           // Aguardando processamento
  CURRENT_BILL: 'CURRENT_BILL', // Na fatura atual
  RESERVED: 'RESERVED',         // Reservada para próxima fatura
  BANK_BALANCE: 'BANK_BALANCE', // Processada no saldo do banco
  PAID: 'PAID',                 // Paga (fatura foi paga)
  OVERDUE: 'OVERDUE'            // Em atraso
};

// Helper functions for status validation
const isValidSubscriptionStatus = (status) => {
  return Object.values(SUBSCRIPTION_STATUS).includes(status);
};

const isValidInstallmentStatus = (status) => {
  return Object.values(INSTALLMENT_STATUS).includes(status);
};

// Status descriptions for UI
const SUBSCRIPTION_STATUS_LABELS = {
  [SUBSCRIPTION_STATUS.PENDING]: 'Aguardando Cobrança',
  [SUBSCRIPTION_STATUS.BILLED]: 'Faturada',
  [SUBSCRIPTION_STATUS.PAID]: 'Paga',
  [SUBSCRIPTION_STATUS.OVERDUE]: 'Em Atraso',
  [SUBSCRIPTION_STATUS.CANCELLED]: 'Cancelada'
};

const INSTALLMENT_STATUS_LABELS = {
  [INSTALLMENT_STATUS.PENDING]: 'Aguardando',
  [INSTALLMENT_STATUS.CURRENT_BILL]: 'Na Fatura',
  [INSTALLMENT_STATUS.RESERVED]: 'Reservada',
  [INSTALLMENT_STATUS.BANK_BALANCE]: 'Processada',
  [INSTALLMENT_STATUS.PAID]: 'Paga',
  [INSTALLMENT_STATUS.OVERDUE]: 'Em Atraso'
};

// Status colors for UI
const SUBSCRIPTION_STATUS_COLORS = {
  [SUBSCRIPTION_STATUS.PENDING]: 'yellow',
  [SUBSCRIPTION_STATUS.BILLED]: 'blue',
  [SUBSCRIPTION_STATUS.PAID]: 'green',
  [SUBSCRIPTION_STATUS.OVERDUE]: 'red',
  [SUBSCRIPTION_STATUS.CANCELLED]: 'gray'
};

const INSTALLMENT_STATUS_COLORS = {
  [INSTALLMENT_STATUS.PENDING]: 'yellow',
  [INSTALLMENT_STATUS.CURRENT_BILL]: 'blue',
  [INSTALLMENT_STATUS.RESERVED]: 'purple',
  [INSTALLMENT_STATUS.BANK_BALANCE]: 'green',
  [INSTALLMENT_STATUS.PAID]: 'green',
  [INSTALLMENT_STATUS.OVERDUE]: 'red'
};

module.exports = {
  SUBSCRIPTION_STATUS,
  INSTALLMENT_STATUS,
  isValidSubscriptionStatus,
  isValidInstallmentStatus,
  SUBSCRIPTION_STATUS_LABELS,
  INSTALLMENT_STATUS_LABELS,
  SUBSCRIPTION_STATUS_COLORS,
  INSTALLMENT_STATUS_COLORS
};
