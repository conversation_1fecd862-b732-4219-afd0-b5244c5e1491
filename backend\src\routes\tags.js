/**
 * @swagger
 * components:
 *   schemas:
 *     Tag:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           description: ID único da tag
 *         name:
 *           type: string
 *           description: Nome da tag
 *         color:
 *           type: string
 *           description: Cor da tag
 */

const express = require('express')
const router = express.Router()
const { PrismaClient } = require('@prisma/client')
const { authenticateToken } = require('../middleware/auth')

const prisma = new PrismaClient()

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken)

/**
 * @swagger
 * /tags:
 *   get:
 *     summary: Lista todas as tags do usuário
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de tags retornada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Tag'
 *       401:
 *         description: Token de autenticação inválido
 *       500:
 *         description: Erro interno do servidor
 */
// Listar todas as tags do usuário
router.get('/', async (req, res) => {
  try {
    const tags = await prisma.tag.findMany({
      where: {
        userId: req.user.id
      },
      orderBy: {
        name: 'asc'
      }
    })

    res.json(tags)
  } catch (error) {
    console.error('Erro ao buscar tags:', error)
    res.status(500).json({ error: 'Erro ao buscar tags' })
  }
})

// Criar uma nova tag
router.post('/', async (req, res) => {
  try {
    const { name, color } = req.body

    if (!name || !color) {
      return res.status(400).json({ error: 'Nome e cor são obrigatórios' })
    }

    const tag = await prisma.tag.create({
      data: {
        name,
        color,
        userId: req.user.id
      }
    })

    res.status(201).json(tag)
  } catch (error) {
    console.error('Erro ao criar tag:', error)
    res.status(500).json({ error: 'Erro ao criar tag' })
  }
})

// Atualizar uma tag
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const { name, color } = req.body

    if (!name || !color) {
      return res.status(400).json({ error: 'Nome e cor são obrigatórios' })
    }

    // Verificar se a tag pertence ao usuário
    const existingTag = await prisma.tag.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    })

    if (!existingTag) {
      return res.status(404).json({ error: 'Tag não encontrada' })
    }

    const tag = await prisma.tag.update({
      where: { id },
      data: {
        name,
        color
      }
    })

    res.json(tag)
  } catch (error) {
    console.error('Erro ao atualizar tag:', error)
    res.status(500).json({ error: 'Erro ao atualizar tag' })
  }
})

// Deletar uma tag
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params

    // Verificar se a tag pertence ao usuário
    const existingTag = await prisma.tag.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    })

    if (!existingTag) {
      return res.status(404).json({ error: 'Tag não encontrada' })
    }

    await prisma.tag.delete({
      where: { id }
    })

    res.json({ message: 'Tag deletada com sucesso' })
  } catch (error) {
    console.error('Erro ao deletar tag:', error)
    res.status(500).json({ error: 'Erro ao deletar tag' })
  }
})

module.exports = router 