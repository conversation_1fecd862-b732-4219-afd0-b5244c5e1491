/**
 * @swagger
 * components:
 *   schemas:
 *     TransactionContact:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           description: ID único do contato
 *         name:
 *           type: string
 *           description: Nome do contato
 *         description:
 *           type: string
 *           description: Descrição do contato
 *         email:
 *           type: string
 *           description: Email(s) do contato
 *         phone:
 *           type: string
 *           description: Telefone(s) do contato
 *         pixAccount:
 *           type: string
 *           description: Conta(s) PIX do contato
 *         photo:
 *           type: string
 *           description: URL da foto do contato
 */

const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const cloudinary = require('../config/cloudinary');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Configuração do multer para upload de fotos
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Apenas imagens são permitidas'));
    }
  }
});

/**
 * @swagger
 * /txcontacts:
 *   get:
 *     summary: Lista todos os contatos de transações
 *     tags: [Transaction Contacts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de contatos retornada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/TransactionContact'
 *       401:
 *         description: Token de autenticação inválido
 *       500:
 *         description: Erro interno do servidor
 */
// Listar contatos de transações
router.get('/', async (req, res) => {
  try {
    const contacts = await prisma.transactionContacts.findMany({
      where: { userId: req.user.id },
      orderBy: { name: 'asc' }
    });

    res.json(contacts);
  } catch (error) {
    console.error('Erro ao buscar contatos de transações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar contato por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const contact = await prisma.transactionContacts.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!contact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    res.json(contact);
  } catch (error) {
    console.error('Erro ao buscar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar contato
router.post('/', async (req, res) => {
  try {
    const { name, description, email, phone, pixAccount } = req.body;

    if (!name || name.trim() === '') {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }

    const contact = await prisma.transactionContacts.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        email: email?.trim() || null,
        phone: phone?.trim() || null,
        pixAccount: pixAccount?.trim() || null,
        userId: req.user.id
      }
    });

    res.status(201).json(contact);
  } catch (error) {
    console.error('Erro ao criar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar contato
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, email, phone, pixAccount } = req.body;

    const contact = await prisma.transactionContacts.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!contact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    if (!name || name.trim() === '') {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }

    const updatedContact = await prisma.transactionContacts.update({
      where: { id },
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        email: email?.trim() || null,
        phone: phone?.trim() || null,
        pixAccount: pixAccount?.trim() || null
      }
    });

    res.json(updatedContact);
  } catch (error) {
    console.error('Erro ao atualizar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar contato
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const contact = await prisma.transactionContacts.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!contact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    await prisma.transactionContacts.delete({
      where: { id }
    });

    res.json({ message: 'Contato deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Upload de foto do contato
router.post('/:id/photo', upload.single('photo'), async (req, res) => {
  try {
    const { id } = req.params;

    const contact = await prisma.transactionContacts.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!contact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    if (!req.file) {
      return res.status(400).json({ error: 'Nenhuma foto foi enviada' });
    }

    let photoUrl = null;

    // Upload da foto para o Cloudinary
    try {
      const result = await new Promise((resolve, reject) => {
        cloudinary.uploader.upload_stream(
          {
            resource_type: 'image',
            folder: 'transactionContacts',
            transformation: [
              { width: 300, height: 300, crop: 'fill', gravity: 'face' }
            ]
          },
          (error, result) => {
            if (error) reject(error);
            else resolve(result);
          }
        ).end(req.file.buffer);
      });

      photoUrl = result.secure_url;
    } catch (uploadError) {
      console.error('Erro no upload da foto:', uploadError);
      return res.status(500).json({ error: 'Erro ao fazer upload da foto' });
    }

    const updatedContact = await prisma.transactionContacts.update({
      where: { id },
      data: { photo: photoUrl }
    });

    res.json(updatedContact);
  } catch (error) {
    console.error('Erro ao fazer upload da foto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar estatísticas do contato
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params;
    const { month, year } = req.query;

    const contact = await prisma.transactionContacts.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!contact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    // Construir filtros de data
    const dateFilter = {};
    if (month && year) {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0, 23, 59, 59);
      dateFilter.date = {
        gte: startDate,
        lte: endDate
      };
    }

    // Buscar transações relacionadas ao contato
    const transactions = await prisma.transaction.findMany({
      where: {
        userId: req.user.id,
        transactionContactsId: id,
        ...dateFilter
      }
    });

    // Calcular estatísticas
    const stats = {
      totalTransactions: transactions.length,
      totalReceived: 0,
      totalSent: 0
    };

    transactions.forEach(transaction => {
      if (transaction.type === 'INCOME') {
        stats.totalReceived += transaction.amount;
      } else {
        stats.totalSent += transaction.amount;
      }
    });

    res.json(stats);
  } catch (error) {
    console.error('Erro ao buscar estatísticas do contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
