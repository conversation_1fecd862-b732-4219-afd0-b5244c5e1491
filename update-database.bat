@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo ⏳ Corrigindo status dos contatos de empréstimos...
call node -e "const { PrismaClient } = require('@prisma/client'); const prisma = new PrismaClient(); async function updateAllContactsStatus() { try { const contacts = await prisma.contact.findMany({ include: { loans: { include: { payments: true } } } }); for (const contact of contacts) { let totalLoans = contact.loans.length; let completedLoans = 0; let onTimeLoans = 0; let lateLoans = 0; contact.loans.forEach(loan => { if (loan.status === 'COMPLETED') { completedLoans++; const hasLatePayments = loan.payments.some(payment => payment.isPaid && payment.isLate); if (hasLatePayments) { lateLoans++; } else { onTimeLoans++; } } }); let status = 'NEUTRAL'; if (completedLoans > 0) { const onTimePercentage = (onTimeLoans / completedLoans) * 100; if (onTimePercentage >= 80) { status = 'GOOD'; } else if (onTimePercentage < 50) { status = 'BAD'; } } await prisma.contact.update({ where: { id: contact.id }, data: { status, totalLoans, paidOnTime: onTimeLoans, latePayments: lateLoans } }); console.log('Contato atualizado:', contact.name, '- Status:', status, '- Em dia:', onTimeLoans, '- Atrasados:', lateLoans); } console.log('Todos os contatos foram atualizados!'); } catch (error) { console.error('Erro:', error); } finally { await prisma.$disconnect(); } } updateAllContactsStatus();"
if %errorlevel% neq 0 (
    echo ❌ Erro ao corrigir status dos contatos
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Sistema Financeiro Completo - Transações e Contatos:
echo.
echo 👥 SISTEMA DE CONTATOS DE TRANSAÇÕES:
echo   ✅ Nova tabela TransactionContact separada
echo   ✅ CRUD completo independente
echo   ✅ Upload de fotos via Cloudinary
echo   ✅ Campos: nome, descrição, email, telefone, PIX
echo   ✅ Estatísticas por contato
echo   ✅ Modal profissional com seções organizadas
echo   ✅ Integração com transações, rotinas e templates
echo.
echo 🔄 ROTINAS APRIMORADAS:
echo   ✅ Pausar rotinas sem deletar itens
echo   ✅ Rota específica PATCH /status
echo   ✅ Campo de contato em vez de link de pagamento
echo   ✅ Integração com novos contatos
echo   ✅ Preservação de dados ao pausar
echo.
echo 📋 TEMPLATES ATUALIZADOS:
echo   ✅ Campo de contato substituindo paymentLink
echo   ✅ Integração com TransactionContact
echo   ✅ Interface atualizada com dropdown de contatos
echo   ✅ Exibição de contato nos cards de template
echo.
echo 🔍 FILTROS AVANÇADOS:
echo   ✅ Filtro por contatos múltiplos
echo   ✅ Interface visual com avatares
echo   ✅ Seleção/deseleção de contatos
echo   ✅ Contador de contatos selecionados
echo   ✅ Integração com sistema de filtros existente
echo.
echo 📸 INTEGRAÇÃO CLOUDINARY EXPANDIDA:
echo   ✅ Upload de fotos de contatos
echo   ✅ Transformações automáticas (300x300)
echo   ✅ Pasta organizada: transaction-contacts
echo   ✅ Fallback para iniciais em caso de erro
echo.
echo 🗄️ BANCO DE DADOS ATUALIZADO:
echo   ✅ Nova tabela TransactionContact
echo   ✅ Campo transactionContactId em Transaction
echo   ✅ Campo transactionContactId em RoutineItem
echo   ✅ Campo transactionContactId em TransactionTemplate
echo   ✅ Índices para performance
echo   ✅ Foreign keys configuradas
echo.
echo 🔧 CORREÇÕES TÉCNICAS:
echo   ✅ Endpoint /transactioncontacts (sem hífen)
echo   ✅ Includes atualizados em todas as consultas
echo   ✅ Serviços separados no frontend
echo   ✅ Compatibilidade mantida com código existente
echo.
echo 🛠️ CORREÇÕES DE EMPRÉSTIMOS:
echo   ✅ Barra de progresso atualiza corretamente ao marcar como quitado
echo   ✅ Transações automáticas na criação de empréstimos
echo   ✅ Saldo do banco atualizado corretamente
echo   ✅ Status dos contatos corrigido (empréstimos vs parcelas)
echo   ✅ Contagem de empréstimos em dia vs atrasados corrigida
echo.
echo 🚀 NOVAS FUNCIONALIDADES DE EMPRÉSTIMOS:
echo   ✅ Lógica corrigida: LOAN_GIVEN = DESPESA, LOAN_RECEIVED = RECEITA
echo   ✅ Pagamentos: LOAN_GIVEN = RECEITA, LOAN_RECEIVED = DESPESA
echo   ✅ Parcelas dinâmicas: Sistema cria automaticamente novas parcelas
echo   ✅ Controle de valor restante: Não permite ultrapassar valor total
echo   ✅ Modal para adicionar parcelas manualmente
echo   ✅ Informações detalhadas de valor pago/restante
echo   ✅ Validação de valores e datas
echo   ✅ Logs detalhados para debug de transações
echo   ✅ Empréstimos minimizados por padrão no modal
echo   ✅ Contador de parcelas adicionadas
echo   ✅ Correção do erro "parcela já foi paga"
echo.
echo 🚨 CORREÇÕES URGENTES APLICADAS:
echo   ✅ userId obrigatório restaurado em todas as parcelas
echo   ✅ Criação de empréstimos corrigida
echo   ✅ Criação de parcelas dinâmicas corrigida
echo   ✅ Upload de comprovantes funcionando (Cloudinary)
echo   ✅ Transações sendo criadas corretamente
echo   ✅ Saldos dos bancos sendo atualizados
echo.
echo 🎯 MELHORIAS PONTUAIS IMPLEMENTADAS:
echo   ✅ Data/hora atual quando empréstimo é criado hoje
echo   ✅ Data/hora atual quando parcela é paga hoje
echo   ✅ Comprovantes copiados para transações automaticamente
echo   ✅ Relação entre transações e empréstimos/parcelas
echo   ✅ Botão "Ir para Empréstimo" no modal de transação
echo   ✅ Navegação entre transações e empréstimos funcionando
echo.
echo 🔧 CORREÇÕES FINAIS APLICADAS:
echo   ✅ Progresso baseado em valores pagos (não parcelas)
echo   ✅ Navegação de transação para empréstimo corrigida
echo   ✅ Upload de comprovantes em transações funcionando
echo   ✅ Contatos exibidos com foto e informações completas
echo   ✅ Links para contatos funcionando no modal de transação
echo   ✅ Interface visual melhorada para contatos
echo.
echo 🎨 MELHORIAS NO MODAL DE TRANSAÇÃO:
echo   ✅ Loop infinito na tela de empréstimos corrigido
echo   ✅ Modal de criação mais profissional e compacto
echo   ✅ Campo de horário adicionado com inteligência de data
echo   ✅ Data atual como padrão nos campos
echo   ✅ Contatos sendo exibidos corretamente no modal
echo   ✅ Redirecionamento para empréstimos com logs de debug
echo.
echo 🔧 CORREÇÕES ESPECÍFICAS FINAIS:
echo   ✅ Modal de transação redesenhado com layout em grid
echo   ✅ Card do contato de empréstimo no modal de detalhes
echo   ✅ Redirecionamento correto para contato do empréstimo
echo   ✅ Rotinas enviando comprovantes corretamente
echo   ✅ Transações de rotina sem nome da rotina
echo   ✅ Flag isFromRoutine adicionada ao schema
echo   ✅ Upload de comprovantes em rotinas funcionando
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
