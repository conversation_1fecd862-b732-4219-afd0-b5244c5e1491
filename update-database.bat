@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo ⏳ Corrigindo status dos contatos de empréstimos...
call node -e "const { PrismaClient } = require('@prisma/client'); const prisma = new PrismaClient(); async function updateAllContactsStatus() { try { const contacts = await prisma.contact.findMany({ include: { loans: { include: { payments: true } } } }); for (const contact of contacts) { let totalLoans = contact.loans.length; let completedLoans = 0; let onTimeLoans = 0; let lateLoans = 0; contact.loans.forEach(loan => { if (loan.status === 'COMPLETED') { completedLoans++; const hasLatePayments = loan.payments.some(payment => payment.isPaid && payment.isLate); if (hasLatePayments) { lateLoans++; } else { onTimeLoans++; } } }); let status = 'NEUTRAL'; if (completedLoans > 0) { const onTimePercentage = (onTimeLoans / completedLoans) * 100; if (onTimePercentage >= 80) { status = 'GOOD'; } else if (onTimePercentage < 50) { status = 'BAD'; } } await prisma.contact.update({ where: { id: contact.id }, data: { status, totalLoans, paidOnTime: onTimeLoans, latePayments: lateLoans } }); console.log('Contato atualizado:', contact.name, '- Status:', status, '- Em dia:', onTimeLoans, '- Atrasados:', lateLoans); } console.log('Todos os contatos foram atualizados!'); } catch (error) { console.error('Erro:', error); } finally { await prisma.$disconnect(); } } updateAllContactsStatus();"
if %errorlevel% neq 0 (
    echo ❌ Erro ao corrigir status dos contatos
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Sistema Financeiro Completo - Transações e Contatos:
echo.
echo 👥 SISTEMA DE CONTATOS DE TRANSAÇÕES:
echo   ✅ Nova tabela TransactionContact separada
echo   ✅ CRUD completo independente
echo   ✅ Upload de fotos via Cloudinary
echo   ✅ Campos: nome, descrição, email, telefone, PIX
echo   ✅ Estatísticas por contato
echo   ✅ Modal profissional com seções organizadas
echo   ✅ Integração com transações, rotinas e templates
echo.
echo 🔄 ROTINAS APRIMORADAS:
echo   ✅ Pausar rotinas sem deletar itens
echo   ✅ Rota específica PATCH /status
echo   ✅ Campo de contato em vez de link de pagamento
echo   ✅ Integração com novos contatos
echo   ✅ Preservação de dados ao pausar
echo.
echo 📋 TEMPLATES ATUALIZADOS:
echo   ✅ Campo de contato substituindo paymentLink
echo   ✅ Integração com TransactionContact
echo   ✅ Interface atualizada com dropdown de contatos
echo   ✅ Exibição de contato nos cards de template
echo.
echo 🔍 FILTROS AVANÇADOS:
echo   ✅ Filtro por contatos múltiplos
echo   ✅ Interface visual com avatares
echo   ✅ Seleção/deseleção de contatos
echo   ✅ Contador de contatos selecionados
echo   ✅ Integração com sistema de filtros existente
echo.
echo 📸 INTEGRAÇÃO CLOUDINARY EXPANDIDA:
echo   ✅ Upload de fotos de contatos
echo   ✅ Transformações automáticas (300x300)
echo   ✅ Pasta organizada: transaction-contacts
echo   ✅ Fallback para iniciais em caso de erro
echo.
echo 🗄️ BANCO DE DADOS ATUALIZADO:
echo   ✅ Nova tabela TransactionContact
echo   ✅ Campo transactionContactId em Transaction
echo   ✅ Campo transactionContactId em RoutineItem
echo   ✅ Campo transactionContactId em TransactionTemplate
echo   ✅ Índices para performance
echo   ✅ Foreign keys configuradas
echo.
echo 🔧 CORREÇÕES TÉCNICAS:
echo   ✅ Endpoint /transactioncontacts (sem hífen)
echo   ✅ Includes atualizados em todas as consultas
echo   ✅ Serviços separados no frontend
echo   ✅ Compatibilidade mantida com código existente
echo.
echo 🛠️ CORREÇÕES DE EMPRÉSTIMOS:
echo   ✅ Barra de progresso atualiza corretamente ao marcar como quitado
echo   ✅ Transações automáticas na criação de empréstimos
echo   ✅ Saldo do banco atualizado corretamente
echo   ✅ Status dos contatos corrigido (empréstimos vs parcelas)
echo   ✅ Contagem de empréstimos em dia vs atrasados corrigida
echo.
echo 🚀 NOVAS FUNCIONALIDADES DE EMPRÉSTIMOS:
echo   ✅ Lógica corrigida: LOAN_GIVEN = DESPESA, LOAN_RECEIVED = RECEITA
echo   ✅ Pagamentos: LOAN_GIVEN = RECEITA, LOAN_RECEIVED = DESPESA
echo   ✅ Parcelas dinâmicas: Sistema cria automaticamente novas parcelas
echo   ✅ Controle de valor restante: Não permite ultrapassar valor total
echo   ✅ Modal para adicionar parcelas manualmente
echo   ✅ Informações detalhadas de valor pago/restante
echo   ✅ Validação de valores e datas
echo   ✅ Logs detalhados para debug de transações
echo   ✅ Empréstimos minimizados por padrão no modal
echo   ✅ Contador de parcelas adicionadas
echo   ✅ Correção do erro "parcela já foi paga"
echo.
echo 🚨 CORREÇÕES URGENTES APLICADAS:
echo   ✅ userId obrigatório restaurado em todas as parcelas
echo   ✅ Criação de empréstimos corrigida
echo   ✅ Criação de parcelas dinâmicas corrigida
echo   ✅ Upload de comprovantes funcionando (Cloudinary)
echo   ✅ Transações sendo criadas corretamente
echo   ✅ Saldos dos bancos sendo atualizados
echo.
echo 🎯 MELHORIAS PONTUAIS IMPLEMENTADAS:
echo   ✅ Data/hora atual quando empréstimo é criado hoje
echo   ✅ Data/hora atual quando parcela é paga hoje
echo   ✅ Comprovantes copiados para transações automaticamente
echo   ✅ Relação entre transações e empréstimos/parcelas
echo   ✅ Botão "Ir para Empréstimo" no modal de transação
echo   ✅ Navegação entre transações e empréstimos funcionando
echo.
echo 🔧 CORREÇÕES FINAIS APLICADAS:
echo   ✅ Progresso baseado em valores pagos (não parcelas)
echo   ✅ Navegação de transação para empréstimo corrigida
echo   ✅ Upload de comprovantes em transações funcionando
echo   ✅ Contatos exibidos com foto e informações completas
echo   ✅ Links para contatos funcionando no modal de transação
echo   ✅ Interface visual melhorada para contatos
echo.
echo 🎨 MELHORIAS NO MODAL DE TRANSAÇÃO:
echo   ✅ Loop infinito na tela de empréstimos corrigido
echo   ✅ Modal de criação mais profissional e compacto
echo   ✅ Campo de horário adicionado com inteligência de data
echo   ✅ Data atual como padrão nos campos
echo   ✅ Contatos sendo exibidos corretamente no modal
echo   ✅ Redirecionamento para empréstimos com logs de debug
echo.
echo 🔧 CORREÇÕES ESPECÍFICAS FINAIS:
echo   ✅ Modal de transação redesenhado com layout em grid
echo   ✅ Card do contato de empréstimo no modal de detalhes
echo   ✅ Redirecionamento correto para contato do empréstimo
echo   ✅ Rotinas enviando comprovantes corretamente
echo   ✅ Transações de rotina sem nome da rotina
echo   ✅ Flag isFromRoutine adicionada ao schema
echo   ✅ Upload de comprovantes em rotinas funcionando
echo.
echo 🚨 CORREÇÕES DE BUGS CRÍTICOS:
echo   ✅ Erro Multer "Unexpected field" corrigido
echo   ✅ Upload.any() para aceitar campos dinâmicos
echo   ✅ Parse correto de FormData em rotinas
echo   ✅ Filtros de data no modal de contatos corrigidos
echo   ✅ Normalização de datas para comparação
echo   ✅ Logs de debug adicionados para filtros
echo.
echo 🎯 TEMPLATES ATUALIZADOS:
echo   ✅ Erro de criação de template corrigido
echo   ✅ Campo tags convertido corretamente (array para string)
echo   ✅ Upload de comprovantes em templates funcionando
echo   ✅ Campo de horário adicionado na execução
echo   ✅ Inteligência de data/hora implementada
echo   ✅ Flag isFromTemplate adicionada ao schema
echo   ✅ FormData configurado para envio de arquivos
echo.
echo 🔧 CORREÇÃO DE IMPORT:
echo   ✅ Middleware de upload importado corretamente
echo   ✅ Destructuring { upload } aplicado
echo   ✅ Erro "upload.single is not a function" corrigido
echo   ✅ Templates funcionando 100%
echo.
echo 🎯 CORREÇÕES FINAIS ESPECÍFICAS:
echo   ✅ Campo transactionContactId corrigido nos templates
echo   ✅ Contatos sendo salvos e exibidos corretamente
echo   ✅ Filtros de período no modal de contatos corrigidos
echo   ✅ Estados sendo atualizados antes das chamadas API
echo   ✅ Parâmetros corretos enviados para o backend
echo.
echo 💳 SISTEMA DE PARCELAS REVOLUCIONADO:
echo   ✅ Nova tabela InstallmentControl criada
echo   ✅ Transação de controle NÃO soma mais na fatura
echo   ✅ Apenas primeira parcela vai para fatura atual
echo   ✅ Próximas parcelas adicionadas automaticamente
echo   ✅ Sistema inteligente de processamento de parcelas
echo   ✅ Serviço InstallmentService implementado
echo   ✅ Rotas /api/installment-controls criadas
echo   ✅ Integração com pagamento de faturas
echo   ✅ Controle preciso de parcelas pendentes
echo.
echo 🎯 MELHORIAS CRÍTICAS IMPLEMENTADAS:
echo   ✅ Parcelas afetam LIMITE DE CRÉDITO, não saldo do banco
echo   ✅ Valor total da compra reduz limite imediatamente
echo   ✅ Pagamento libera limite gradualmente por parcela
echo   ✅ Limite NUNCA ultrapassa valor máximo configurado
echo   ✅ Verificação de limite antes de criar parcelas
echo   ✅ Logs detalhados de movimentação de limite
echo   ✅ Proteção contra overflow de limite de crédito
echo.
echo 🔧 CORREÇÕES FINAIS DE PARCELAMENTO:
echo   ✅ TODAS as parcelas criadas imediatamente
echo   ✅ Nome das parcelas SEM numeração (1/2, 2/2)
echo   ✅ Sistema usa tags para identificar parcelas
echo   ✅ Apenas primeira parcela na fatura atual
echo   ✅ Demais parcelas marcadas como "pagas" até serem processadas
echo   ✅ Serviço move parcelas para fatura (não cria novas)
echo   ✅ Comportamento correto de parcelamento implementado
echo.
echo 📅 SISTEMA DE CICLOS DE FATURAMENTO IMPLEMENTADO:
echo   ✅ Parcelas antes do fechamento reduzem saldo do banco
echo   ✅ Parcelas após fechamento vão para fatura
echo   ✅ Lógica baseada no dia de fechamento configurado
echo   ✅ Serviço BillingCycleService criado
echo   ✅ Rotas /api/billing-cycles implementadas
echo   ✅ Integração com sistema de parcelas
echo   ✅ Processamento automático de ciclos
echo   ✅ Sincronização de transações com ciclos
echo   ✅ Comportamento realista de cartão de crédito
echo.
echo 🔧 CORREÇÃO CRÍTICA DA LÓGICA DE FECHAMENTO:
echo   ✅ Lógica corrigida: compara com próximo fechamento atual
echo   ✅ Não mais com fechamento do mês da parcela
echo   ✅ Exemplo: Hoje 11/06, fechamento dia 12
echo   ✅ Parcela 01/06 deve ir para fatura (não reduzir saldo)
echo   ✅ Logs detalhados para debug implementados
echo   ✅ Comportamento correto de cartão real
echo.
echo 🎯 LÓGICA REAL DE CARTÃO IMPLEMENTADA:
echo   ✅ Período de fatura baseado no último fechamento
echo   ✅ Transações no período atual vão para fatura
echo   ✅ Transações fora do período reduzem saldo
echo   ✅ Exemplo: Fechamento dia 12, hoje 11/06
echo   ✅ Período atual: 13/05 até 12/06
echo   ✅ Parcela 01/06: Dentro do período = FATURA
echo   ✅ Tabela de transações: scroll horizontal corrigido
echo.
echo 💳 CORREÇÃO CRÍTICA DO LIMITE DISPONÍVEL:
echo   ✅ Limite reduzido APENAS pelo valor que vai para fatura
echo   ✅ Parcelas que reduzem saldo NÃO afetam limite
echo   ✅ Cálculo separado: totalForBill vs totalForBankBalance
echo   ✅ Verificação de limite apenas para valor da fatura
echo   ✅ Logs detalhados de distribuição de valores
echo   ✅ Comportamento correto: limite preservado para faturas pagas
echo.
echo 🔧 CORREÇÕES FINAIS CRÍTICAS:
echo   ✅ Constraint InstallmentControl corrigida (onDelete: Cascade)
echo   ✅ Agora é possível deletar formas de pagamento
echo   ✅ Duplicação de lógica de parcelamento removida
echo   ✅ Cálculo correto: 30 reais = 10 saldo + 20 limite
echo   ✅ Fatura atual deve mostrar valor correto
echo   ✅ Sistema funcionando como cartão real
echo.
echo 🎯 LÓGICA REAL DE CARTÃO IMPLEMENTADA (CORRIGIDA):
echo   ✅ 3 cenários implementados corretamente:
echo   ✅ 1. No período atual → VAI PARA FATURA ATUAL
echo   ✅ 2. Antes do período → REDUZ SALDO (fatura já paga)
echo   ✅ 3. Após o período → FICA RESERVADA (próxima fatura)
echo   ✅ Limite reduzido: fatura atual + reservado
echo   ✅ Parcelas futuras não reduzem saldo
echo   ✅ Sistema idêntico aos bancos reais
echo.
echo 🔄 SISTEMA DE FATURAS E ASSINATURAS CORRIGIDO:
echo   ✅ Pagamento de fatura processa parcelas reservadas
echo   ✅ Parcelas reservadas movidas para nova fatura
echo   ✅ Assinaturas processadas automaticamente no dia correto
echo   ✅ Assinaturas respeitam ciclo de faturamento
echo   ✅ Processamento diário: /api/billing-cycles/process-daily
echo   ✅ Sistema completo funcionando como bancos reais
echo.
echo 💳 CÁLCULO DE FATURA CORRIGIDO:
echo   ✅ Fatura consulta parcelas que deveriam estar no período atual
echo   ✅ Transações reservadas movidas automaticamente para fatura
echo   ✅ Sincronização automática de faturas com ciclo de faturamento
echo   ✅ Rota de sincronização: /api/billing-cycles/sync-bank-bills/:bankId
echo   ✅ Faturas dos cartões atualizadas automaticamente
echo   ✅ Parcelamentos aparecem corretamente na fatura do mês
echo.
echo 🔒 CONTROLE DE ATUALIZAÇÃO DE FATURA IMPLEMENTADO:
echo   ✅ Fatura só pode ser atualizada se estiver paga
echo   ✅ Verificação automática antes de processar fechamento
echo   ✅ Proteção contra mudança de fatura com pendências
echo   ✅ Logs informativos sobre bloqueios
echo.
echo 📅 SISTEMA DE DATA SIMULADA PARA TESTES:
echo   ✅ Serviço DateService criado para simular datas
echo   ✅ Rotas /api/date-simulator para controle de data
echo   ✅ Integração com sistema de ciclos de faturamento
echo   ✅ Interface na tela de Configurações
echo   ✅ Atalhos rápidos: amanhã, próxima semana, próximo mês
echo   ✅ Sistema completo para testes de faturamento
echo.
echo 🔄 FLUXO DE PAGAMENTO DE FATURA CORRIGIDO:
echo   ✅ Transações marcadas como pagas após pagamento
echo   ✅ Processamento correto de parcelas reservadas
echo   ✅ Parcelas movidas para nova fatura conforme período
echo   ✅ Saldo do banco atualizado corretamente
echo   ✅ Fluxo completo funcionando como bancos reais
echo.
echo 📊 SISTEMA DE STATUS IMPLEMENTADO:
echo   ✅ Status de assinaturas: PENDING, BILLED, PAID, OVERDUE, CANCELLED
echo   ✅ Status de parcelas: PENDING, CURRENT_BILL, RESERVED, BANK_BALANCE, PAID, OVERDUE
echo   ✅ Controle granular sobre faturamento e pagamentos
echo   ✅ Constantes JavaScript para type safety
echo   ✅ Labels e cores para interface
echo.
echo 📅 SISTEMA DE DATA SIMULADA CORRIGIDO:
echo   ✅ DateService integrado em todos os pontos críticos
echo   ✅ Criação de transações usa data simulada
echo   ✅ Ciclos de faturamento usam data simulada
echo   ✅ Interface funcional na tela de Configurações
echo   ✅ Sistema completo para testes de faturamento
echo.
echo 💳 NOVO FLUXO DE FATURAS IMPLEMENTADO:
echo   ✅ Status de faturas: PENDING, PAID, OVERDUE
echo   ✅ Atualização automática baseada na data de vencimento
echo   ✅ Bloqueio de transações quando fatura está em atraso
echo   ✅ Pagamento de fatura com verificação de saldo
echo   ✅ Histórico de faturas pagas
echo   ✅ Cálculo automático baseado em transações CURRENT_BILL
echo   ✅ Geração automática de nova fatura quando valor <= 0
echo   ✅ Processamento de próximas parcelas após pagamento
echo   ✅ API completa: /api/bills
echo   ✅ Integração com sistema de parcelamentos
echo   ✅ Verificação de fatura em atraso na criação de transações
echo.
echo 📦 NOVO FLUXO DE PARCELAMENTO IMPLEMENTADO:
echo   ✅ REGRA 1: Processamento automático RESERVED → CURRENT_BILL
echo   ✅ REGRA 2: Parcelas antigas vão direto para BANK_BALANCE
echo   ✅ REGRA 4: Parcelas no período atual vão para CURRENT_BILL
echo   ✅ REGRA 5: Parcelas futuras ficam RESERVED
echo   ✅ REGRA 6: Pagamento marca parcelas CURRENT_BILL como PAID
echo   ✅ Lógica baseada em data de vencimento da fatura
echo   ✅ Processamento automático quando fatura está PENDING
echo   ✅ Cálculo correto de períodos de faturamento
echo   ✅ Integração completa com sistema de faturas
echo   ✅ API para estatísticas: /api/bills/installments/stats/:bankId
echo   ✅ API para forçar processamento: /api/bills/process-reserved/:bankId
echo   ✅ Logs detalhados para debugging
echo.
echo 📋 NOVO FLUXO DE ASSINATURAS IMPLEMENTADO:
echo   ✅ REGRA 0: Processamento automático RESERVED → CURRENT_BILL
echo   ✅ REGRA 1: Assinaturas CURRENT_BILL incluídas no cálculo da fatura
echo   ✅ REGRA 2: Assinaturas futuras ficam RESERVED
echo   ✅ REGRA 3: Assinaturas antigas perguntam ao usuário (paga ou não)
echo   ✅ REGRA 4: Assinaturas no período atual vão para CURRENT_BILL
echo   ✅ REGRA 5: Pagamento marca assinaturas CURRENT_BILL como PAID
echo   ✅ REGRA 6: Nova fatura move assinaturas PAID → RESERVED
echo   ✅ Lógica baseada em dia de cobrança vs data de vencimento
echo   ✅ Processamento automático quando fatura está PENDING
echo   ✅ Integração completa com sistema de faturas
echo   ✅ API para estatísticas: /api/subscriptions/stats/:bankId
echo   ✅ API para forçar processamento: /api/subscriptions/process-reserved
echo   ✅ Interface para escolha do usuário na criação
echo   ✅ Logs detalhados para debugging
echo.
echo 🔧 CORREÇÕES APLICADAS:
echo   ✅ Campo isPaidThisMonth removido das consultas
echo   ✅ Todas as consultas atualizadas para usar campo status
echo   ✅ Compatibilidade com novo schema de assinaturas
echo   ✅ Servidor funcionando sem erros
echo   ✅ Integração completa entre faturas, parcelamentos e assinaturas
echo.
echo 🔒 CONTROLE DE ATUALIZAÇÃO DE FATURA IMPLEMENTADO:
echo   ✅ Fatura só pode ser atualizada se estiver paga
echo   ✅ Verificação automática antes de processar fechamento
echo   ✅ Proteção contra mudança de fatura com pendências
echo   ✅ Logs informativos sobre bloqueios
echo.
echo 📅 SISTEMA DE DATA SIMULADA PARA TESTES:
echo   ✅ Serviço DateService criado para simular datas
echo   ✅ Rotas /api/date-simulator para controle de data
echo   ✅ Integração com sistema de ciclos de faturamento
echo   ✅ Atalhos rápidos: amanhã, próxima semana, próximo mês
echo   ✅ Função especial: ir para dia de fechamento do banco
echo   ✅ Sistema completo para testes de faturamento
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
