import React, { useState, useEffect } from 'react'
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Eye,
  EyeOff,
  TrendingUp
} from 'lucide-react'
import { bankService } from '../services/bankService'
import { paymentMethodService } from '../services/bankService'
import toast from 'react-hot-toast'

function BillManager() {
  const [banks, setBanks] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [showBalances, setShowBalances] = useState(true)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [banksData, paymentMethodsData] = await Promise.all([
        bankService.getBanks(),
        paymentMethodService.getPaymentMethods()
      ])
      setBanks(banksData)
      setPaymentMethods(paymentMethodsData)
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handlePayBill = async (bankId) => {
    try {
      const response = await bankService.payBill(bankId)
      toast.success(`Fatura paga com sucesso! ${response.transactionsCreated} transações criadas.`)
      fetchData()
    } catch (error) {
      console.error('Erro ao pagar fatura:', error)
      toast.error(error.response?.data?.error || 'Erro ao pagar fatura')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('pt-BR')
  }

  const getBillTotal = (bankId) => {
    const creditMethods = paymentMethods.filter(
      method => method.bankId === bankId && method.type === 'CREDIT'
    )
    return creditMethods.reduce((sum, method) => sum + (method.currentBill || 0), 0)
  }

  const getNextDueDate = (bank) => {
    if (!bank.billDueDay) return null
    
    const today = new Date()
    const dueDate = new Date(today.getFullYear(), today.getMonth(), bank.billDueDay)
    
    if (dueDate < today) {
      dueDate.setMonth(dueDate.getMonth() + 1)
    }
    
    return dueDate
  }

  const getDaysUntilDue = (bank) => {
    const dueDate = getNextDueDate(bank)
    if (!dueDate) return null
    
    const today = new Date()
    const diffTime = dueDate - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  const getBillStatus = (bank, creditMethods) => {
    if (creditMethods.length === 0) return 'no-card'
    
    const totalBill = creditMethods.reduce((sum, method) => sum + (method.currentBill || 0), 0)
    const hasUnpaidBill = creditMethods.some(method => !method.isBillPaid && method.currentBill > 0)
    
    if (totalBill === 0) return 'no-bill'
    if (hasUnpaidBill) return 'pending'
    return 'paid'
  }

  // Filtrar apenas bancos com limite de crédito
  const banksWithCredit = banks.filter(bank => bank.creditLimit > 0)

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const totalPending = banksWithCredit.reduce((sum, bank) => {
    return sum + getBillTotal(bank.id)
  }, 0)

  const banksWithBills = banksWithCredit.filter(bank => getBillTotal(bank.id) > 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Faturas dos Cartões</h2>
          <p className="text-gray-600 mt-1">
            Gerencie as faturas dos seus cartões de crédito por banco
          </p>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowBalances(!showBalances)}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {showBalances ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {showBalances ? 'Ocultar Valores' : 'Mostrar Valores'}
          </button>
          <div className="text-right">
            <p className="text-sm text-gray-600">Total Pendente</p>
            <p className="text-2xl font-bold text-red-600">
              {showBalances ? formatCurrency(totalPending) : '••••••••'}
            </p>
          </div>
        </div>
      </div>

      {/* Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <CreditCard className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-900">Bancos com Crédito</span>
          </div>
          <p className="text-2xl font-bold text-blue-600">{banksWithCredit.length}</p>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <span className="font-medium text-red-900">Com Faturas</span>
          </div>
          <p className="text-2xl font-bold text-red-600">{banksWithBills.length}</p>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-900">Limite Total</span>
          </div>
          <p className="text-2xl font-bold text-green-600">
            {showBalances ? formatCurrency(banksWithCredit.reduce((sum, bank) => sum + bank.creditLimit, 0)) : '••••••••'}
          </p>
        </div>
      </div>

      {/* Lista de Faturas */}
      {banksWithCredit.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
          <CreditCard className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum cartão de crédito encontrado
          </h3>
          <p className="text-gray-600 mb-6">
            Configure o limite de crédito nos seus bancos para ver as faturas aqui
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {banksWithCredit.map((bank) => {
            const creditMethods = paymentMethods.filter(
              method => method.bankId === bank.id && method.type === 'CREDIT'
            )
            const billStatus = getBillStatus(bank, creditMethods)
            const billTotal = getBillTotal(bank.id)
            const daysUntilDue = getDaysUntilDue(bank)
            const nextDueDate = getNextDueDate(bank)

            return (
              <div
                key={bank.id}
                className="bg-white rounded-xl border shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="p-6">
                  {/* Header do Card */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl"
                        style={{ backgroundColor: bank.color + '20' }}
                      >
                        {bank.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {bank.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Limite: {showBalances ? formatCurrency(bank.creditLimit) : '••••••'}
                        </p>
                      </div>
                    </div>
                    
                    {/* Status da Fatura */}
                    {billStatus === 'pending' && (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    {billStatus === 'paid' && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {billStatus === 'no-bill' && (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                  </div>

                  {/* Informações da Fatura */}
                  <div className="space-y-3">
                    <div className="bg-gray-50 rounded-xl p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-600">Fatura Atual</span>
                        <span className={`text-xl font-bold ${
                          billTotal > 0 ? 'text-red-600' : 'text-gray-900'
                        }`}>
                          {showBalances ? formatCurrency(billTotal) : '••••••••'}
                        </span>
                      </div>
                      
                      {nextDueDate && (
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-500">Vencimento</span>
                          <span className={`font-medium ${
                            daysUntilDue <= 3 ? 'text-red-600' : 'text-gray-700'
                          }`}>
                            {formatDate(nextDueDate)}
                            {daysUntilDue !== null && (
                              <span className="ml-1">
                                ({daysUntilDue} dias)
                              </span>
                            )}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Limite Disponível */}
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-500">Limite Disponível</span>
                      <span className="text-blue-600 font-medium">
                        {showBalances ? formatCurrency(bank.availableLimit || 0) : '••••••'}
                      </span>
                    </div>

                    {/* Utilização do Limite */}
                    {bank.creditLimit > 0 && (
                      <div>
                        <div className="flex justify-between items-center text-sm mb-1">
                          <span className="text-gray-500">Utilização</span>
                          <span className="text-gray-700 font-medium">
                            {((bank.creditLimit - (bank.availableLimit || 0)) / bank.creditLimit * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${(bank.creditLimit - (bank.availableLimit || 0)) / bank.creditLimit * 100}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Ações */}
                  {creditMethods.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <button
                        onClick={() => handlePayBill(bank.id)}
                        className={`w-full flex items-center justify-center gap-2 px-4 py-2 text-white rounded-lg transition-colors ${
                          billTotal > 0
                            ? 'bg-green-600 hover:bg-green-700'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        <CheckCircle className="h-4 w-4" />
                        {billTotal > 0 ? 'Pagar Fatura' : 'Pagar Fatura do Mês'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default BillManager
